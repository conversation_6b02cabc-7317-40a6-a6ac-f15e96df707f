import React from 'react';
import { Activity } from '@/hooks/use-activities';
import { <PERSON><PERSON>, Di<PERSON>Content, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { UserAvatar } from '@/components/user/UserAvatar';
import { formatDistanceToNow, format } from 'date-fns';
import { MapPin, Calendar, Clock, Users, DollarSign, MessageCircle, Share2, ExternalLink } from 'lucide-react';
import { useAuth } from '@/hooks/use-auth';
import { useActivityQueue, useJoinQueue } from '@/hooks/use-activity-queue';
import { ActivityShareModal } from '@/components/activity/ActivityShareModal';
import { ActivityJoin } from '@/components/activity/ActivityJoin';
import { useProfile } from '@/hooks/use-profile';
import { Link } from 'react-router-dom';
import { ActivityChatButton } from '@/components/activity/ActivityChatButton';
import { Separator } from '@/components/ui/separator';
import { ActivityQueueParticipants } from '@/components/activity/ActivityQueueParticipants';
import { ActivityQueueStatus } from '@/components/activity/ActivityQueueStatus';

interface ActivityDetailsModalProps {
  activity: Activity | null;
  isOpen: boolean;
  onClose: () => void;
}

export function ActivityDetailsModal({
  activity,
  isOpen,
  onClose
}: ActivityDetailsModalProps) {
  const [showShareModal, setShowShareModal] = React.useState(false);
  const { user } = useAuth();
  const { data: queueEntries, isLoading: queueLoading } = useActivityQueue(activity?.id);
  const { data: hostProfile } = useProfile(activity?.host_id);
  const { mutate: joinQueue, isPending: isJoining } = useJoinQueue();
  
  const handleJoinActivity = () => {
    if (!user || !activity) return;
    
    joinQueue({
      activityId: activity.id,
      userId: user.id
    });
  };
  
  const isUserInQueue = React.useMemo(() => {
    if (!user || !queueEntries) return false;
    return queueEntries.some(entry => entry.user_id === user.id);
  }, [user, queueEntries]);
  
  const participantCount = React.useMemo(() => {
    if (!queueEntries) return 0;
    return queueEntries.filter(entry => entry.status === 'confirmed').length;
  }, [queueEntries]);

  const getActivityDateInfo = () => {
    if (!activity) return {};
    
    const start = new Date(activity.start_time);
    const end = activity.end_time ? new Date(activity.end_time) : null;
    
    return {
      date: format(start, 'EEEE, MMMM d, yyyy'),
      startTime: format(start, 'h:mm a'),
      endTime: end ? format(end, 'h:mm a') : null,
      timeAgo: formatDistanceToNow(start, { addSuffix: true })
    };
  };

  const dateInfo = activity ? getActivityDateInfo() : {};

  // Safely check if user is host
  const isHost = user?.id && activity?.host_id ? activity.host_id === user.id : false;
  
  // Find the user's entry in the queue (if they have one)
  const userEntry = user && queueEntries ? 
    queueEntries.find(entry => entry.user_id === user.id) : 
    undefined;

  if (!activity) return null;

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-lg max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <div className="flex items-center gap-2 mb-1">
              <span className="text-2xl">{activity.category?.icon || '📌'}</span>
              <Badge variant="outline">{activity.category?.name || 'Activity'}</Badge>
              {activity.is_paid && (
                <Badge variant="secondary" className="bg-amber-100 text-amber-700 border-amber-200">
                  <DollarSign className="h-3 w-3 mr-1" /> 
                  {activity.price ? `$${activity.price}` : 'Paid'}
                </Badge>
              )}
            </div>
            
            <DialogTitle className="text-xl">{activity.title}</DialogTitle>
            <DialogDescription>
              <div className="flex items-center gap-1.5">
                <span>Hosted by {activity.host?.display_name || hostProfile?.display_name || 'Unknown'}</span>
                <span className="text-xs text-muted-foreground">• {dateInfo.timeAgo}</span>
              </div>
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            {/* Media Section */}
            {activity.media_urls && activity.media_urls.length > 0 && (
              <div className="w-full h-48 bg-muted rounded-md overflow-hidden">
                <img 
                  src={activity.media_urls[0]} 
                  alt={activity.title} 
                  className="w-full h-full object-cover"
                />
              </div>
            )}
            
            {/* Description */}
            <div className="text-sm">
              <p>{activity.description || 'No description provided.'}</p>
            </div>
            
            <Separator />
            
            {/* Details */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              {/* Date and Time */}
              <div className="space-y-1">
                <div className="flex items-center text-sm">
                  <Calendar className="h-4 w-4 mr-2 text-blue-500" />
                  <span>{dateInfo.date}</span>
                </div>
                
                <div className="flex items-center text-sm">
                  <Clock className="h-4 w-4 mr-2 text-purple-500" />
                  <span>
                    {dateInfo.startTime}
                    {dateInfo.endTime && ` - ${dateInfo.endTime}`}
                  </span>
                </div>
              </div>
              
              {/* Location and Participants */}
              <div className="space-y-1">
                {activity.address && (
                  <div className="flex items-center text-sm">
                    <MapPin className="h-4 w-4 mr-2 text-red-500" />
                    <span>{activity.address}</span>
                  </div>
                )}
                
                <div className="flex items-center text-sm">
                  <Users className="h-4 w-4 mr-2 text-green-500" />
                  <span>
                    {participantCount} joined
                    {activity.max_participants && ` (${participantCount}/${activity.max_participants})`}
                  </span>
                </div>
              </div>
            </div>
            
            {/* Host Info */}
            <div className="bg-muted/50 p-3 rounded-md">
              <div className="flex items-center gap-2">
                <UserAvatar 
                  user={activity.host || hostProfile} 
                  size="md" 
                  className="border-2 border-background" 
                />
                <div>
                  <p className="font-medium">{activity.host?.display_name || hostProfile?.display_name || 'Unknown'}</p>
                  {hostProfile?.bio && (
                    <p className="text-xs text-muted-foreground line-clamp-1">{hostProfile.bio}</p>
                  )}
                </div>
              </div>
            </div>
            
            {/* Queue Status for current user */}
            {userEntry && (
              <ActivityQueueStatus
                activity={activity}
                userEntry={userEntry}
                confirmedCount={participantCount}
                totalCapacity={activity.max_participants || null}
                onLeaveQueue={() => {}}
                isProcessing={false}
              />
            )}
            
            {/* Participants list for host only */}
            {isHost && queueEntries && queueEntries.length > 0 && (
              <div>
                <h3 className="font-medium mb-2">Participants</h3>
                <ActivityQueueParticipants
                  entries={queueEntries}
                  isHost={isHost}
                />
              </div>
            )}
            
            <Separator />
            
            {/* Actions */}
            <div className="grid grid-cols-2 gap-2">
              <ActivityJoin 
                activity={activity} 
                size="default" 
                className="w-full" 
              />
              
              <ActivityChatButton 
                activity={activity} 
                className="w-full"
              />
            </div>
            
            <div className="flex gap-2 pt-2">
              <Button 
                variant="outline" 
                className="flex-1"
                onClick={() => setShowShareModal(true)}
              >
                <Share2 className="h-4 w-4 mr-2" />
                Share
              </Button>
              
              <Button 
                variant="outline" 
                className="flex-1"
                asChild
              >
                <Link to={`/activity/${activity.id}`}>
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Full Details
                </Link>
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
      
      {activity && (
        <ActivityShareModal
          activity={activity}
          isOpen={showShareModal}
          onClose={() => setShowShareModal(false)}
        />
      )}
    </>
  );
}
