
import React, { useState } from 'react';
import { MainLayout } from "@/components/layouts/MainLayout";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Slider } from "@/components/ui/slider";
import ProfileDetailModal from "@/components/hire/ProfileDetailModal";
import BookingModal from "@/components/hire/BookingModal";
import ServiceBookingModal from "@/components/hire/ServiceBookingModal";
import { ServiceProviderRegistration } from "@/components/hire/ServiceProviderRegistration";
import { useBuddyProviders } from "@/hooks/use-buddy-providers";
import { useServiceCategories, useUserServiceProvider } from "@/hooks/use-service-provider";
import { BuddyProvider } from "@/hooks/use-buddy-providers";
import { BookingData } from "@/types/hire";
import { Users, Search, MapPin, DollarSign, Filter, Plus, Loader2 } from "lucide-react";

const HirePage: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedPurpose, setSelectedPurpose] = useState<string>("");
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 100]);
  const [selectedDistance, setSelectedDistance] = useState<string>("any");
  const [selectedProfile, setSelectedProfile] = useState<BuddyProvider | null>(null);
  const [isProfileModalOpen, setIsProfileModalOpen] = useState(false);
  const [isBookingModalOpen, setIsBookingModalOpen] = useState(false);
  const [isRegistrationModalOpen, setIsRegistrationModalOpen] = useState(false);

  const { data: providers, isLoading, error } = useBuddyProviders(undefined, searchQuery);
  const { data: categories } = useServiceCategories();
  const { data: userProvider } = useUserServiceProvider();

  const handleViewProfile = (profile: BuddyProvider) => {
    setSelectedProfile(profile);
    setIsProfileModalOpen(true);
  };

  const handleHireProfile = (profile: BuddyProvider) => {
    setSelectedProfile(profile);
    setIsBookingModalOpen(true);
  };

  const handleBookingConfirm = (bookingData: BookingData) => {
    // This is now handled by the ServiceBookingModal component
    setIsBookingModalOpen(false);
  };

  // Filter providers based on search criteria
  const filteredProviders = providers?.filter(provider => {
    const matchesPrice = provider.hourlyRate >= priceRange[0] && provider.hourlyRate <= priceRange[1];
    const matchesPurpose = !selectedPurpose || provider.hirePurposes.includes(selectedPurpose);
    return matchesPrice && matchesPurpose;
  }) || [];

  if (isLoading) {
    return (
      <MainLayout title="Hire a Buddy">
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </MainLayout>
    );
  }

  if (error) {
    return (
      <MainLayout title="Hire a Buddy">
        <div className="text-center py-8">
          <p className="text-red-600">Error loading buddy providers: {error.message}</p>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout title="Hire a Buddy">
      <div className="container mx-auto px-4 py-8">
        {/* Header Section */}
        <Card className="mb-8 border-none bg-gradient-to-r from-primary/10 to-secondary/10 shadow-sm">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <Users className="h-5 w-5 text-primary" />
                  <CardTitle>Hire a Buddy</CardTitle>
                </div>
                <CardDescription>
                  Connect with local buddies for hangouts, guided tours, or event companionship.
                  Browse profiles, check ratings, and book time with amazing people in your area.
                </CardDescription>
              </div>
              {!userProvider && (
                <Button onClick={() => setIsRegistrationModalOpen(true)} className="gap-2">
                  <Plus className="h-4 w-4" />
                  Become a Buddy
                </Button>
              )}
            </div>
          </CardHeader>
        </Card>

        {/* Search and Filters Section */}
        <Card className="mb-8 border shadow-sm">
          <CardHeader>
            <div className="flex items-center gap-2 mb-2">
              <Filter className="h-5 w-5 text-muted-foreground" />
              <CardTitle className="text-lg">Search & Filters</CardTitle>
            </div>
            <div className="space-y-4">
              <div className="flex gap-4 flex-wrap">
                <div className="relative flex-1 max-w-md">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search by name, interest, or location..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-9"
                  />
                </div>
                <Select value={selectedPurpose} onValueChange={setSelectedPurpose}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Purpose" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Purposes</SelectItem>
                    <SelectItem value="Hangout">Hang Out</SelectItem>
                    <SelectItem value="Local Guide">Local Guide</SelectItem>
                    <SelectItem value="Event Buddy">Event Buddy</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={selectedDistance} onValueChange={setSelectedDistance}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Distance" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="nearby">Nearby</SelectItem>
                    <SelectItem value="city">City</SelectItem>
                    <SelectItem value="any">Any Distance</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center gap-4">
                <DollarSign className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm text-gray-500">Price Range:</span>
                <Slider
                  value={priceRange}
                  onValueChange={(value) => setPriceRange(value as [number, number])}
                  max={100}
                  step={5}
                  className="w-[200px]"
                />
                <span className="text-sm">${priceRange[0]} - ${priceRange[1]}/hr</span>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Profiles Grid */}
        {filteredProviders.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredProviders.map((profile) => (
              <Card key={profile.id} className="p-4 hover:shadow-lg transition-shadow">
                <div className="flex items-start gap-4">
                  <Avatar className="h-16 w-16">
                    <AvatarImage src={profile.avatarUrl} />
                    <AvatarFallback>{profile.displayName[0]}</AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <h3 className="font-semibold">{profile.displayName}</h3>
                    <p className="text-sm text-gray-500">{profile.username}</p>
                    <div className="flex items-center gap-1 mt-1">
                      <span className="text-yellow-400">⭐</span>
                      <span className="text-sm">{profile.rating}</span>
                      <span className="text-sm text-gray-500">({profile.reviewCount})</span>
                    </div>
                  </div>
                </div>
                <p className="mt-3 text-sm text-gray-600 line-clamp-2">{profile.bio}</p>
                <div className="mt-3 flex flex-wrap gap-2">
                  {profile.vibes.slice(0, 3).map((vibe) => (
                    <Badge key={vibe} variant="secondary">{vibe}</Badge>
                  ))}
                </div>
                <div className="mt-4 flex items-center justify-between">
                  <div className="text-sm">
                    <span className="font-semibold">${profile.hourlyRate}/hr</span>
                    <span className="text-gray-500 ml-2">• {profile.distance.toFixed(1)}km away</span>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleViewProfile(profile)}
                    >
                      View
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => handleHireProfile(profile)}
                    >
                      Hire
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No buddy providers found</h3>
            <p className="text-gray-600 mb-4">
              {searchQuery || selectedPurpose
                ? "Try adjusting your search or filters"
                : "Be the first to register as a buddy provider!"}
            </p>
            {!userProvider && (
              <Button onClick={() => setIsRegistrationModalOpen(true)}>
                Become a Buddy Provider
              </Button>
            )}
          </div>
        )}

        {/* Modals */}
        {selectedProfile && (
          <>
            <ProfileDetailModal
              profile={selectedProfile}
              isOpen={isProfileModalOpen}
              onClose={() => setIsProfileModalOpen(false)}
            />
            <ServiceBookingModal
              provider={{
                id: selectedProfile.id,
                name: selectedProfile.displayName,
                hourlyRate: selectedProfile.hourlyRate,
                avatarUrl: selectedProfile.avatarUrl
              }}
              isOpen={isBookingModalOpen}
              onClose={() => setIsBookingModalOpen(false)}
            />
          </>
        )}

        <ServiceProviderRegistration
          isOpen={isRegistrationModalOpen}
          onClose={() => setIsRegistrationModalOpen(false)}
        />
      </div>
    </MainLayout>
  );
};

export default HirePage;
