
import { useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/use-auth';
import { ChatMessage } from '@/types/chat';

export interface GroupChatMessage extends ChatMessage {
  is_system_message?: boolean;
  sender?: {
    id: string;
    display_name?: string;
    avatar_url?: string;
    username?: string;
  } | null;
}

interface ActivityGroupChatOptions {
  activityId: string;
}

interface ActivityGroupChatHookReturn {
  conversation: { id: string } | null;
  isLoadingConversation: boolean;
  messages: GroupChatMessage[];
  isLoadingMessages: boolean;
  participants: any[];
  isLoadingParticipants: boolean;
  sendMessage: {
    mutate: (message: string) => void;
    isPending: boolean;
  };
}

interface ConversationData {
  id: string;
  activity_id?: string;
  is_group: boolean;
  last_message?: string;
  last_message_at?: string;
  created_at: string;
  updated_at: string;
}

export function useActivityGroupChat(options: ActivityGroupChatOptions): ActivityGroupChatHookReturn {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const activityId = options.activityId;

  // Get or create group chat for activity - simplified to avoid deep instantiation
  const { data: groupChat, isLoading: isChatLoading } = useQuery({
    queryKey: ['activity-group-chat', activityId],
    queryFn: async () => {
      if (!activityId || !user) return null;

      try {
        // Check if group chat already exists for this activity
        const { data: existingChat } = await supabase
          .from('chat_conversations')
          .select('*')
          .eq('activity_id', activityId)
          .eq('is_group', true)
          .maybeSingle();

        if (existingChat) {
          return existingChat;
        }

        // For now, return null if no chat exists to avoid complex creation logic
        return null;
      } catch (error) {
        console.error('Error fetching group chat:', error);
        return null;
      }
    },
    enabled: !!activityId && !!user
  });

  // Get messages for the group chat - simplified
  const { data: messages, isLoading: isMessagesLoading } = useQuery({
    queryKey: ['activity-group-messages', groupChat?.id],
    queryFn: async (): Promise<GroupChatMessage[]> => {
      if (!groupChat?.id) return [];

      try {
        const { data, error } = await supabase
          .from('messages')
          .select('*')
          .eq('conversation_id', groupChat.id)
          .order('created_at', { ascending: true });

        if (error) throw error;

        // Return messages without complex profile joining to avoid type issues
        return (data || []).map(msg => ({
          ...msg,
          sender: null // Simplified - no profile lookup for now
        })) as GroupChatMessage[];
      } catch (error) {
        console.error('Error fetching messages:', error);
        return [];
      }
    },
    enabled: !!groupChat?.id
  });

  // Send message mutation - simplified
  const sendMessage = useMutation({
    mutationFn: async (content: string) => {
      if (!user || !groupChat?.id) {
        throw new Error("Missing required information to send message");
      }

      const { data, error } = await supabase
        .from('messages')
        .insert({
          sender_id: user.id,
          conversation_id: groupChat.id,
          content: content.trim(),
          message_type: 'text'
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      if (groupChat?.id) {
        queryClient.invalidateQueries({
          queryKey: ['activity-group-messages', groupChat.id]
        });
      }
    },
    onError: (error) => {
      console.error("Error sending message:", error);
    }
  });

  // Real-time subscription for new messages
  useEffect(() => {
    if (!groupChat?.id) return;

    const channel = supabase
      .channel(`activity-chat:${groupChat.id}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: `conversation_id=eq.${groupChat.id}`
        },
        () => {
          queryClient.invalidateQueries({
            queryKey: ['activity-group-messages', groupChat.id]
          });
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [groupChat?.id, queryClient]);

  return {
    conversation: groupChat ? { id: groupChat.id } : null,
    isLoadingConversation: isChatLoading,
    messages: messages || [],
    isLoadingMessages: isMessagesLoading,
    participants: [],
    isLoadingParticipants: false,
    sendMessage: {
      mutate: sendMessage.mutate,
      isPending: sendMessage.isPending
    }
  };
}
