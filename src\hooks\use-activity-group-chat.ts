
import { useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/use-auth';
import { ChatMessage } from '@/types/chat';

export interface GroupChatMessage extends ChatMessage {
  is_system_message?: boolean;
  sender?: {
    id: string;
    display_name?: string;
    avatar_url?: string;
    username?: string;
  } | null;
}

interface ActivityGroupChatOptions {
  activityId: string;
}

interface ActivityGroupChatHookReturn {
  conversation: { id: string } | null;
  isLoadingConversation: boolean;
  messages: GroupChatMessage[];
  isLoadingMessages: boolean;
  participants: any[];
  isLoadingParticipants: boolean;
  sendMessage: {
    mutate: (message: string) => void;
    isPending: boolean;
  };
}

interface ConversationData {
  id: string;
  activity_id?: string;
  is_group: boolean;
  last_message?: string;
  last_message_at?: string;
  created_at: string;
  updated_at: string;
}

export function useActivityGroupChat(options: ActivityGroupChatOptions): ActivityGroupChatHookReturn {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const activityId = options.activityId;

  // Get or create group chat for activity
  const { data: groupChat, isLoading: isChatLoading } = useQuery({
    queryKey: ['activity-group-chat', activityId],
    queryFn: async (): Promise<ConversationData | null> => {
      if (!activityId || !user) return null;

      // Check if group chat already exists for this activity
      const { data: existingChat } = await supabase
        .from('chat_conversations')
        .select('*')
        .eq('activity_id', activityId)
        .eq('is_group', true)
        .maybeSingle();

      if (existingChat) {
        // Check if user is already a participant
        const { data: participant } = await supabase
          .from('chat_participants')
          .select('*')
          .eq('conversation_id', existingChat.id)
          .eq('user_id', user.id)
          .maybeSingle();

        if (!participant) {
          // Add user as participant
          await supabase
            .from('chat_participants')
            .insert({
              conversation_id: existingChat.id,
              user_id: user.id
            });
        }

        return existingChat as ConversationData;
      }

      // Create new group chat if user is host
      const { data: activity } = await supabase
        .from('activities')
        .select('host_id')
        .eq('id', activityId)
        .maybeSingle();

      const isHost = user.id === activity?.host_id;
      if (isHost) {
        const { data: newChat, error } = await supabase
          .from('chat_conversations')
          .insert({
            activity_id: activityId,
            is_group: true,
            last_message: 'Group chat created for activity',
            last_message_at: new Date().toISOString()
          })
          .select()
          .single();

        if (error) throw error;

        // Add host as participant
        await supabase
          .from('chat_participants')
          .insert({
            conversation_id: newChat.id,
            user_id: user.id
          });

        return newChat as ConversationData;
      }

      return null;
    },
    enabled: !!activityId && !!user
  });

  // Get messages for the group chat
  const { data: messages, isLoading: isMessagesLoading } = useQuery({
    queryKey: ['activity-group-messages', groupChat?.id],
    queryFn: async (): Promise<GroupChatMessage[]> => {
      if (!groupChat?.id) return [];

      const { data, error } = await supabase
        .from('messages')
        .select('*')
        .eq('conversation_id', groupChat.id)
        .order('created_at', { ascending: true });

      if (error) throw error;

      // Fetch sender profiles separately to avoid join issues
      const senderIds = [...new Set((data || []).map(msg => msg.sender_id).filter(Boolean))];
      const { data: profiles } = await supabase
        .from('profiles')
        .select('id, display_name, avatar_url, username')
        .in('id', senderIds);

      const profileMap = new Map(profiles?.map(p => [p.id, p]) || []);

      return (data || []).map(msg => ({
        ...msg,
        sender: msg.sender_id ? profileMap.get(msg.sender_id) || {
          id: msg.sender_id,
          display_name: 'Unknown User',
          avatar_url: '',
          username: 'unknown'
        } : null
      })) as GroupChatMessage[];
    },
    enabled: !!groupChat?.id
  });

  // Send message mutation
  const sendMessage = useMutation({
    mutationFn: async (content: string) => {
      if (!user || !groupChat?.id) {
        throw new Error("Missing required information to send message");
      }

      const { data, error } = await supabase
        .from('messages')
        .insert({
          sender_id: user.id,
          conversation_id: groupChat.id,
          content: content.trim(),
          message_type: 'text'
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      if (groupChat?.id) {
        queryClient.invalidateQueries({
          queryKey: ['activity-group-messages', groupChat.id]
        });
      }
    },
    onError: (error) => {
      console.error("Error sending message:", error);
    }
  });

  // Real-time subscription for new messages
  useEffect(() => {
    if (!groupChat?.id) return;

    const channel = supabase
      .channel(`activity-chat:${groupChat.id}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: `conversation_id=eq.${groupChat.id}`
        },
        () => {
          queryClient.invalidateQueries({
            queryKey: ['activity-group-messages', groupChat.id]
          });
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [groupChat?.id, queryClient]);

  return {
    conversation: groupChat ? { id: groupChat.id } : null,
    isLoadingConversation: isChatLoading,
    messages: messages || [],
    isLoadingMessages: isMessagesLoading,
    participants: [],
    isLoadingParticipants: false,
    sendMessage: {
      mutate: sendMessage.mutate,
      isPending: sendMessage.isPending
    }
  };
}
