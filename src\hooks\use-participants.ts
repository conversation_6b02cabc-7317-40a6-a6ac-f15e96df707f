
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export interface Participant {
  user_id: string;
  display_name?: string;
  avatar_url?: string;
  username?: string;
}

export function useParticipants(conversationId?: string) {
  return useQuery({
    queryKey: ['participants', conversationId],
    queryFn: async (): Promise<Participant[]> => {
      if (!conversationId) return [];

      const { data, error } = await supabase
        .from('chat_participants')
        .select(`
          user_id,
          user:profiles!chat_participants_user_id_fkey (
            display_name,
            avatar_url,
            username
          )
        `)
        .eq('conversation_id', conversationId);

      if (error) throw error;

      return (data || []).map(participant => ({
        user_id: participant.user_id,
        display_name: participant.user?.display_name || '',
        avatar_url: participant.user?.avatar_url || '',
        username: participant.user?.username || ''
      })) as Participant[];
    },
    enabled: !!conversationId
  });
}
