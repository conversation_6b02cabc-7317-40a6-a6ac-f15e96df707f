
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/use-auth';
import { useToast } from '@/hooks/use-toast';

export interface Gig {
  id: string;
  provider_id: string;
  category_id?: string;
  title: string;
  description: string;
  short_description?: string;
  price_starting: number;
  delivery_time_days: number;
  revisions_included: number;
  gig_type: 'service' | 'digital' | 'consultation';
  skill_level: 'beginner' | 'intermediate' | 'expert';
  packages: any[];
  add_ons: any[];
  requirements_from_buyer?: string;
  gallery_images: string[];
  gallery_videos: string[];
  tags: string[];
  faq: any[];
  is_active: boolean;
  is_featured: boolean;
  total_orders: number;
  total_reviews: number;
  average_rating: number;
  completion_rate: number;
  response_time_hours: number;
  last_order_at?: string;
  created_at: string;
  updated_at: string;
  provider?: {
    id: string;
    business_name: string;
    user_id: string;
    user?: {
      display_name?: string;
      avatar_url?: string;
      username?: string;
    };
  };
  category?: {
    name: string;
    icon?: string;
  };
}

export function useGigs(categoryId?: string, searchQuery?: string) {
  return useQuery({
    queryKey: ['gigs', categoryId, searchQuery],
    queryFn: async (): Promise<Gig[]> => {
      let query = supabase
        .from('gigs')
        .select(`
          *,
          provider:service_providers!gigs_provider_id_fkey (
            id,
            business_name,
            user_id,
            user:profiles!service_providers_user_id_fkey (
              display_name,
              avatar_url,
              username
            )
          ),
          category:service_categories!gigs_category_id_fkey (
            name,
            icon
          )
        `)
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (categoryId) {
        query = query.eq('category_id', categoryId);
      }

      if (searchQuery) {
        query = query.or(`title.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%,tags.cs.{${searchQuery}}`);
      }

      const { data, error } = await query;

      if (error) throw error;

      return (data || []).map(gig => ({
        ...gig,
        provider: gig.provider ? {
          id: gig.provider.id || '',
          business_name: gig.provider.business_name || '',
          user_id: gig.provider.user_id || '',
          user: gig.provider.user ? {
            display_name: gig.provider.user.display_name || '',
            avatar_url: gig.provider.user.avatar_url || '',
            username: gig.provider.user.username || ''
          } : undefined
        } : undefined,
        category: gig.category ? {
          name: gig.category.name || '',
          icon: gig.category.icon || ''
        } : undefined
      })) as Gig[];
    }
  });
}

export function useUserGigs() {
  const { user } = useAuth();

  return useQuery({
    queryKey: ['user-gigs', user?.id],
    queryFn: async (): Promise<Gig[]> => {
      if (!user) return [];

      const { data, error } = await supabase
        .from('gigs')
        .select(`
          *,
          provider:service_providers!gigs_provider_id_fkey (
            id,
            business_name,
            user_id
          ),
          category:service_categories!gigs_category_id_fkey (
            name,
            icon
          )
        `)
        .eq('service_providers.user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;

      return (data || []).map(gig => ({
        ...gig,
        provider: gig.provider ? {
          id: gig.provider.id || '',
          business_name: gig.provider.business_name || '',
          user_id: gig.provider.user_id || ''
        } : undefined,
        category: gig.category ? {
          name: gig.category.name || '',
          icon: gig.category.icon || ''
        } : undefined
      })) as Gig[];
    },
    enabled: !!user
  });
}

export function useCreateGig() {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (gigData: Partial<Gig>) => {
      if (!user) throw new Error('You must be logged in to create a gig');

      // First, get or create service provider profile
      let { data: provider } = await supabase
        .from('service_providers')
        .select('id')
        .eq('user_id', user.id)
        .single();

      if (!provider) {
        const { data: newProvider, error: providerError } = await supabase
          .from('service_providers')
          .insert({
            user_id: user.id,
            business_name: gigData.title || 'My Business',
            description: 'Professional service provider'
          })
          .select('id')
          .single();

        if (providerError) throw providerError;
        provider = newProvider;
      }

      const { data, error } = await supabase
        .from('gigs')
        .insert({
          ...gigData,
          provider_id: provider.id
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['gigs'] });
      queryClient.invalidateQueries({ queryKey: ['user-gigs'] });
      toast({
        title: 'Success!',
        description: 'Your gig has been created successfully',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive'
      });
    }
  });
}
