
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/use-auth';

export interface Gig {
  id: string;
  title: string;
  description: string;
  short_description?: string;
  price_starting: number;
  delivery_time_days: number;
  category_id?: string;
  gig_type: 'service' | 'digital' | 'consultation';
  skill_level: 'beginner' | 'intermediate' | 'advanced';
  tags: string[];
  requirements_from_buyer?: string;
  gallery_images: string[];
  gallery_videos: string[];
  packages: any[];
  add_ons: any[];
  faq: any[];
  user_id: string;
  provider_id?: string;
  hourly_rate?: number;
  rating: number;
  total_orders: number;
  is_featured: boolean;
  availability: string;
  created_at: string;
  updated_at: string;
  // Add missing properties for GigCard
  average_rating: number;
  total_reviews: number;
  provider?: {
    id: string;
    business_name?: string;
    user?: {
      id: string;
      display_name?: string;
      avatar_url?: string;
      username?: string;
    };
  };
}

interface CreateGigData {
  title: string;
  description: string;
  short_description?: string;
  price_starting: number;
  delivery_time_days: number;
  category_id?: string;
  gig_type: 'service' | 'digital' | 'consultation';
  skill_level: 'beginner' | 'intermediate' | 'advanced';
  tags: string[];
  requirements_from_buyer?: string;
  gallery_images: string[];
  gallery_videos: string[];
  packages: any[];
  add_ons: any[];
  faq: any[];
}

export function useGigs() {
  return useQuery({
    queryKey: ['gigs'],
    queryFn: async (): Promise<Gig[]> => {
      // Return mock data for now since we don't have gigs table
      return [];
    }
  });
}

export function useCreateGig() {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (gigData: CreateGigData) => {
      if (!user) throw new Error('User not authenticated');

      // Mock implementation for now
      console.log('Creating gig with data:', gigData);
      
      return {
        id: crypto.randomUUID(),
        ...gigData,
        user_id: user.id,
        rating: 0,
        total_orders: 0,
        is_featured: false,
        availability: 'available',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        average_rating: 0,
        total_reviews: 0,
        provider: {
          id: user.id,
          business_name: 'Mock Provider',
          user: {
            id: user.id,
            display_name: 'Mock User',
            avatar_url: '',
            username: 'mockuser'
          }
        }
      };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['gigs'] });
    }
  });
}
