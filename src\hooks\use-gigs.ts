
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/use-auth';

interface Gig {
  id: string;
  title: string;
  description: string;
  short_description?: string;
  price_starting: number;
  delivery_time_days: number;
  category_id?: string;
  gig_type: 'service' | 'digital' | 'consultation';
  skill_level: 'beginner' | 'intermediate' | 'advanced';
  tags: string[];
  requirements_from_buyer?: string;
  gallery_images: string[];
  gallery_videos: string[];
  packages: any[];
  add_ons: any[];
  faq: any[];
  user_id: string;
  provider_id?: string;
  hourly_rate?: number;
  rating: number;
  total_orders: number;
  is_featured: boolean;
  availability: string;
  created_at: string;
  updated_at: string;
}

interface CreateGigData {
  title: string;
  description: string;
  short_description?: string;
  price_starting: number;
  delivery_time_days: number;
  category_id?: string;
  gig_type: 'service' | 'digital' | 'consultation';
  skill_level: 'beginner' | 'intermediate' | 'advanced';
  tags: string[];
  requirements_from_buyer?: string;
  gallery_images: string[];
  gallery_videos: string[];
  packages: any[];
  add_ons: any[];
  faq: any[];
}

export function useGigs() {
  return useQuery({
    queryKey: ['gigs'],
    queryFn: async (): Promise<Gig[]> => {
      const { data, error } = await supabase
        .from('gigs')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    }
  });
}

export function useCreateGig() {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (gigData: CreateGigData) => {
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('gigs')
        .insert({
          ...gigData,
          user_id: user.id,
          rating: 0,
          total_orders: 0,
          is_featured: false,
          availability: 'available'
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['gigs'] });
    }
  });
}
