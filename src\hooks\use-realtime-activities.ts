
import { useState, useEffect } from 'react';
import { Activity } from '@/types/activity';
import { useActivities } from './use-activities';

export function useRealtimeActivities() {
  const { data: initialActivities, isLoading, error, refetch } = useActivities();
  const [activities, setActivities] = useState<Activity[]>([]);

  useEffect(() => {
    if (initialActivities) {
      setActivities(initialActivities);
    }
  }, [initialActivities]);

  // Use polling instead of real-time subscriptions
  useEffect(() => {
    // Poll for activity updates every 20 seconds
    const interval = setInterval(() => {
      refetch();
    }, 20000);

    return () => {
      clearInterval(interval);
    };
  }, [refetch]);

  return {
    activities,
    isLoading,
    error,
  };
}
