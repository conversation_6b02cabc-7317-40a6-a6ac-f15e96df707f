
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export interface BuddyProvider {
  id: string;
  displayName: string;
  username: string;
  avatarUrl: string;
  bio: string;
  vibes: string[];
  location: string;
  distance: number;
  hourlyRate: number;
  rating: number;
  reviewCount: number;
  isAvailable: boolean;
  hirePurposes: string[];
}

export function useBuddyProviders(categoryId?: string, searchQuery?: string) {
  return useQuery({
    queryKey: ['buddy-providers', categoryId, searchQuery],
    queryFn: async (): Promise<BuddyProvider[]> => {
      let query = supabase
        .from('service_providers')
        .select(`
          *,
          user:profiles!service_providers_user_id_fkey (
            display_name,
            avatar_url,
            username,
            bio,
            vibes,
            location
          )
        `)
        .eq('verification_status', 'verified')
        .order('average_rating', { ascending: false });

      if (searchQuery) {
        query = query.or(`business_name.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%`);
      }

      const { data, error } = await query;

      if (error) throw error;

      return (data || []).map(provider => ({
        id: provider.id,
        displayName: provider.user?.display_name || provider.business_name,
        username: provider.user?.username || '',
        avatarUrl: provider.user?.avatar_url || '',
        bio: provider.user?.bio || provider.description || '',
        vibes: provider.user?.vibes || provider.tags || [],
        location: provider.location || '',
        distance: Math.random() * 10, // TODO: Calculate actual distance
        hourlyRate: provider.hourly_rate || 50,
        rating: provider.average_rating || 0,
        reviewCount: provider.total_reviews || 0,
        isAvailable: true,
        hirePurposes: ['Hangout', 'Local Guide', 'Event Buddy'] // TODO: Get from services
      })) as BuddyProvider[];
    }
  });
}
