
import React, { useRef, useEffect } from 'react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Loader2 } from 'lucide-react';
import { Message, ChatMessage } from '@/types/chat';
import { ChatMessage as ChatMessageComponent } from './ChatMessage';
import { TypingIndicator } from './TypingIndicator';
import { useTypingIndicator } from '@/hooks/use-typing-indicator';
import { useReadReceipts } from '@/hooks/use-read-receipts';

interface ChatMessagesProps {
  messages: Message[];
  isLoading: boolean;
  proposals: any[];
  conversationId?: string;
}

export function ChatMessages({ messages, isLoading, proposals, conversationId }: ChatMessagesProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { typingUsers } = conversationId ? useTypingIndicator(conversationId) : { typingUsers: [] };
  const { markAsRead } = conversationId ? useReadReceipts(conversationId) : { markAsRead: () => Promise.resolve() };

  // Scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages, typingUsers]);

  // Mark last message as read
  useEffect(() => {
    if (messages.length > 0 && conversationId) {
      const lastMessage = messages[messages.length - 1];
      markAsRead(lastMessage.id).catch(err => {
        console.error('Error marking message as read:', err);
      });
    }
  }, [messages, conversationId, markAsRead]);

  if (isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <ScrollArea className="flex-1 p-4">
      <div className="space-y-4">
        {/* Render proposals if any */}
        {proposals && proposals.length > 0 && (
          <div className="mb-4">
            {/* Proposals would be rendered here */}
          </div>
        )}

        {/* Render messages */}
        {messages.map((message, index) => {
          // Convert Message to ChatMessage for compatibility
          const chatMessage: ChatMessage = {
            ...message,
            recipient_id: message.recipient_id || '', // Ensure recipient_id is not undefined
          };
          
          return (
            <ChatMessageComponent
              key={message.id}
              message={chatMessage}
              isLastMessage={index === messages.length - 1}
              showSender={index === 0 || messages[index - 1]?.sender_id !== message.sender_id}
              conversationId={conversationId || ''}
            />
          );
        })}

        {/* Typing indicator */}
        {typingUsers.length > 0 && (
          <TypingIndicator users={typingUsers} className="mt-2" />
        )}

        {/* Invisible element to scroll to */}
        <div ref={messagesEndRef} />
      </div>
    </ScrollArea>
  );
}
