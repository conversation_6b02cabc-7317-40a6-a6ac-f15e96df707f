import React, { useState } from 'react';
import { MainLayout } from '@/components/layouts/MainLayout';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useUserBookings, useProviderBookings, useUpdateBookingStatus } from '@/hooks/use-service-bookings';
import { useServiceBookingPayment } from '@/hooks/use-service-booking-payment';
import { Calendar, Clock, DollarSign, User, Loader2 } from 'lucide-react';
import { format } from 'date-fns';

const BookingsPage: React.FC = () => {
  const { data: clientBookings, isLoading: isLoadingClient } = useUserBookings();
  const { data: providerBookings, isLoading: isLoadingProvider } = useProviderBookings();
  const updateStatus = useUpdateBookingStatus();
  const processPayment = useServiceBookingPayment();

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'accepted': return 'bg-blue-100 text-blue-800';
      case 'in_progress': return 'bg-purple-100 text-purple-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      case 'disputed': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleStatusUpdate = async (bookingId: string, status: any) => {
    await updateStatus.mutateAsync({ bookingId, status, isProvider: true });
  };

  const handlePayment = async (bookingId: string) => {
    await processPayment.mutateAsync(bookingId);
  };

  if (isLoadingClient || isLoadingProvider) {
    return (
      <MainLayout title="My Bookings">
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout title="My Bookings">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">My Bookings</h1>
          <p className="text-gray-600">Manage your service bookings and requests</p>
        </div>

        <Tabs defaultValue="client" className="space-y-6">
          <TabsList>
            <TabsTrigger value="client">As Client ({clientBookings?.length || 0})</TabsTrigger>
            <TabsTrigger value="provider">As Provider ({providerBookings?.length || 0})</TabsTrigger>
          </TabsList>

          <TabsContent value="client" className="space-y-4">
            {clientBookings && clientBookings.length > 0 ? (
              clientBookings.map((booking) => (
                <Card key={booking.id} className="p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="text-lg font-semibold">{booking.title}</h3>
                      <p className="text-gray-600">{booking.description}</p>
                    </div>
                    <Badge className={getStatusColor(booking.status)}>
                      {booking.status.replace('_', ' ').toUpperCase()}
                    </Badge>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">{booking.provider?.name}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">${booking.price}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">
                        {booking.scheduled_for ? format(new Date(booking.scheduled_for), 'MMM dd, yyyy') : 'TBD'}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">
                        {format(new Date(booking.created_at), 'MMM dd, yyyy')}
                      </span>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    {booking.status === 'pending' && (
                      <Button
                        size="sm"
                        onClick={() => handlePayment(booking.id)}
                        disabled={processPayment.isPending}
                      >
                        {processPayment.isPending ? <Loader2 className="h-4 w-4 animate-spin" /> : 'Pay Now'}
                      </Button>
                    )}
                    {booking.status === 'accepted' && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleStatusUpdate(booking.id, 'cancelled')}
                        disabled={updateStatus.isPending}
                      >
                        Cancel
                      </Button>
                    )}
                  </div>
                </Card>
              ))
            ) : (
              <div className="text-center py-12">
                <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No bookings yet</h3>
                <p className="text-gray-600">Your service bookings will appear here</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="provider" className="space-y-4">
            {providerBookings && providerBookings.length > 0 ? (
              providerBookings.map((booking) => (
                <Card key={booking.id} className="p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="text-lg font-semibold">{booking.title}</h3>
                      <p className="text-gray-600">{booking.description}</p>
                    </div>
                    <Badge className={getStatusColor(booking.status)}>
                      {booking.status.replace('_', ' ').toUpperCase()}
                    </Badge>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">{booking.client?.display_name}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">${booking.price}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">
                        {booking.scheduled_for ? format(new Date(booking.scheduled_for), 'MMM dd, yyyy') : 'TBD'}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">
                        {format(new Date(booking.created_at), 'MMM dd, yyyy')}
                      </span>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    {booking.status === 'pending' && (
                      <>
                        <Button
                          size="sm"
                          onClick={() => handleStatusUpdate(booking.id, 'accepted')}
                          disabled={updateStatus.isPending}
                        >
                          Accept
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleStatusUpdate(booking.id, 'cancelled')}
                          disabled={updateStatus.isPending}
                        >
                          Decline
                        </Button>
                      </>
                    )}
                    {booking.status === 'accepted' && (
                      <Button
                        size="sm"
                        onClick={() => handleStatusUpdate(booking.id, 'in_progress')}
                        disabled={updateStatus.isPending}
                      >
                        Start Work
                      </Button>
                    )}
                    {booking.status === 'in_progress' && (
                      <Button
                        size="sm"
                        onClick={() => handleStatusUpdate(booking.id, 'completed')}
                        disabled={updateStatus.isPending}
                      >
                        Mark Complete
                      </Button>
                    )}
                  </div>
                </Card>
              ))
            ) : (
              <div className="text-center py-12">
                <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No booking requests</h3>
                <p className="text-gray-600">Service requests will appear here when clients book your services</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
};

export default BookingsPage;
