
import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import Stripe from "https://esm.sh/stripe@14.21.0";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

const logStep = (step: string, details?: any) => {
  const detailsStr = details ? ` - ${JSON.stringify(details)}` : '';
  console.log(`[CREATE-CHECKOUT] ${step}${detailsStr}`);
};

// Handle CORS preflight requests
serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    logStep("Function started");

    const stripeKey = Deno.env.get("STRIPE_SECRET_KEY");
    if (!stripeKey) throw new Error("STRIPE_SECRET_KEY is not set");
    logStep("Stripe key verified");

    // Create Supabase client with anon key for authentication
    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_ANON_KEY") ?? ""
    );
    
    // Get the user from the authorization header
    const authHeader = req.headers.get("Authorization");
    if (!authHeader) throw new Error("No authorization header provided");
    logStep("Authorization header found");
    
    const token = authHeader.replace("Bearer ", "");
    const { data: userData, error: userError } = await supabaseClient.auth.getUser(token);
    if (userError) throw new Error(`Authentication error: ${userError.message}`);
    
    const user = userData.user;
    if (!user?.email) throw new Error("User not authenticated or email not available");
    logStep("User authenticated", { userId: user.id, email: user.email });

    // Parse request body
    const requestData = await req.json();
    logStep("Request data parsed", requestData);
    
    // Initialize Stripe
    const stripe = new Stripe(stripeKey, { apiVersion: "2023-10-16" });
    
    // Check if a Stripe customer record exists for this user
    const customers = await stripe.customers.list({ email: user.email, limit: 1 });
    let customerId;
    
    if (customers.data.length > 0) {
      customerId = customers.data[0].id;
      logStep("Found existing customer", { customerId });
    } else {
      // Create a new customer
      const newCustomer = await stripe.customers.create({
        email: user.email,
        metadata: { userId: user.id }
      });
      customerId = newCustomer.id;
      logStep("Created new customer", { customerId });
    }

    // Handle different checkout types
    let session;
    
    if (requestData.type === "add_funds") {
      // Process wallet funds addition
      session = await stripe.checkout.sessions.create({
        customer: customerId,
        payment_method_types: ['card'],
        line_items: [
          {
            price_data: {
              currency: 'usd',
              product_data: {
                name: 'Wallet Funds',
                description: 'Add funds to your BuddySurf wallet'
              },
              unit_amount: requestData.amount || 2500, // Default $25 if not specified
            },
            quantity: 1,
          },
        ],
        mode: 'payment',
        success_url: `${req.headers.get("origin")}/wallet?success=true`,
        cancel_url: `${req.headers.get("origin")}/wallet?canceled=true`,
        metadata: {
          type: 'add_funds',
          user_id: user.id,
          amount: requestData.amount || 2500
        }
      });
      logStep("Created add funds session", { sessionId: session.id });
    } else if (requestData.activityId) {
      // Process activity payment
      // Get activity details
      const { data: activity, error: activityError } = await supabaseClient
        .from('activities')
        .select('*')
        .eq('id', requestData.activityId)
        .single();
      
      if (activityError) throw new Error(`Error fetching activity: ${activityError.message}`);
      if (!activity) throw new Error("Activity not found");
      if (!activity.is_paid) throw new Error("This activity is not a paid activity");
      
      logStep("Activity found", { activityId: activity.id, title: activity.title });

      // Create checkout session for activity payment
      session = await stripe.checkout.sessions.create({
        customer: customerId,
        payment_method_types: ['card'],
        line_items: [
          {
            price_data: {
              currency: 'usd',
              product_data: {
                name: activity.title || 'Activity Payment',
                description: activity.description || 'Payment for joining an activity'
              },
              unit_amount: Math.round((activity.price || 0) * 100) // Convert from dollars to cents
            },
            quantity: 1,
          },
        ],
        mode: 'payment',
        success_url: `${req.headers.get("origin")}/activity?id=${requestData.activityId}&success=true`,
        cancel_url: `${req.headers.get("origin")}/activity?id=${requestData.activityId}&canceled=true`,
        metadata: {
          type: 'activity_payment',
          activity_id: requestData.activityId,
          user_id: user.id
        }
      });
      logStep("Created activity payment session", { sessionId: session.id, activityId: activity.id });
    } else if (requestData.planId) {
      // Subscription payment (monthly/yearly plans)
      // Price IDs should be defined in your Stripe account
      const planPriceIds: Record<string, string> = {
        weekly: 'price_weekly', // Replace with actual Stripe price IDs
        monthly: 'price_monthly',
        lifetime: 'price_lifetime'
      };
      
      const priceId = planPriceIds[requestData.planId];
      if (!priceId) throw new Error(`Invalid plan ID: ${requestData.planId}`);
      
      // For subscription plans (weekly/monthly)
      if (['weekly', 'monthly'].includes(requestData.planId)) {
        session = await stripe.checkout.sessions.create({
          customer: customerId,
          line_items: [{ price: priceId, quantity: 1 }],
          mode: 'subscription',
          success_url: `${req.headers.get("origin")}/subscription?success=true`,
          cancel_url: `${req.headers.get("origin")}/subscription?canceled=true`,
          metadata: {
            type: 'subscription',
            plan: requestData.planId,
            user_id: user.id
          }
        });
      } 
      // For one-time payments (lifetime)
      else {
        session = await stripe.checkout.sessions.create({
          customer: customerId,
          line_items: [{ price: priceId, quantity: 1 }],
          mode: 'payment',
          success_url: `${req.headers.get("origin")}/subscription?success=true`,
          cancel_url: `${req.headers.get("origin")}/subscription?canceled=true`,
          metadata: {
            type: 'lifetime_subscription',
            plan: requestData.planId,
            user_id: user.id
          }
        });
      }
      logStep("Created subscription session", { sessionId: session.id, planId: requestData.planId });
    } else {
      throw new Error("Invalid request: missing type, activityId, or planId");
    }

    return new Response(JSON.stringify({ 
      url: session.url,
      sessionId: session.id
    }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 200,
    });
    
  } catch (error: any) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error("[CREATE-CHECKOUT] Error:", errorMessage);
    return new Response(JSON.stringify({ error: errorMessage }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 500,
    });
  }
});
