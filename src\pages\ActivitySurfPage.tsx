
import React from 'react';
import { MainLayout } from "@/components/layouts/MainLayout";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { useNavigate } from 'react-router-dom';
import { ActivityUnavailable } from '@/components/activity/ActivityUnavailable';
import { withActivityRouter } from '@/components/activity/ActivityRouter';
import { ActivityCard } from '@/components/activity/ActivityCard';
import { useActivities } from '@/hooks/use-activities';

function ActivitySurfPage() {
  const navigate = useNavigate();
  const { activities, isLoading, error } = useActivities();

  return (
    <MainLayout>
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Activity Surf</h1>
              <p className="text-gray-600 mt-2">
                Discover and join activities happening around you
              </p>
            </div>
            <Button onClick={() => navigate('/activity/create')} className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white hover:from-blue-600 hover:to-indigo-700">
              <Plus className="h-4 w-4 mr-2" />
              Create Activity
            </Button>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        {error ? (
          <ActivityUnavailable
            description="The activities feature is currently unavailable. The necessary database tables may have been removed or not yet configured."
            showSetupButton={true}
          />
        ) : isLoading ? (
          <div className="text-center py-12">Loading activities...</div>
        ) : activities && activities.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {activities.map(activity => (
              <ActivityCard
                key={activity.id}
                activity={activity}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <h2 className="text-xl font-semibold mb-2">No activities found</h2>
            <p className="text-muted-foreground mb-6">Be the first to create an activity!</p>
            <Button onClick={() => navigate('/activity/create')}>
              <Plus className="h-4 w-4 mr-2" />
              Create Activity
            </Button>
          </div>
        )}
      </div>
    </MainLayout>
  );
}

// Wrap with ActivityRouter to enable activity routing functionality
export default withActivityRouter(ActivitySurfPage);
