
import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
import { ThemeProvider } from "@/components/ui/theme-provider";
import { Toaster } from "@/components/ui/toaster";
import HomePage from "@/pages/HomePage";
import ProfilePage from "@/pages/ProfilePage";
import NotFound from "@/pages/NotFound";
import HelpPage from "@/pages/HelpPage";
import SettingsPage from "@/pages/SettingsPage";
import MeetMapPage from "@/pages/MeetMapPage";
import MeetupPage from "@/pages/MeetupPage";
import EarnPage from "@/pages/EarnPage";
import NotificationsPage from "@/pages/NotificationsPage";
import ActivityPage from "@/pages/ActivityPage";
import ActivitySurfPage from "@/pages/ActivitySurfPage";
import CreateActivityPage from "@/pages/CreateActivityPage";
import HirePage from "@/pages/HirePage";
import ChatPage from "@/pages/ChatPage";
import WalletPage from "@/pages/WalletPage";
import AdminPage from "@/pages/AdminPage";
import SubscriptionPage from "@/pages/SubscriptionPage";
import NetworkPage from "@/pages/NetworkPage";
import GigsPage from "./pages/GigsPage";
import DatePickerTest from "@/pages/DatePickerTest";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { AuthProvider } from "./hooks/use-auth";
import { RequireAuth } from "./components/auth/RequireAuth";
import { RequireOnboarding } from "./components/auth/RequireOnboarding";
import { OnboardingController } from "./components/onboarding/OnboardingController";
import { useEffect } from "react";
import { initializeDatabase } from "./utils/create-missing-tables";

function App() {
  const queryClient = new QueryClient();

  // Initialize the database when the app starts
  useEffect(() => {
    initializeDatabase().catch(console.error);
  }, []);

  return (
    <ThemeProvider defaultTheme="light" storageKey="vite-ui-theme">
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <Router>
            <Routes>
              {/* Public routes */}
              <Route path="/" element={<HomePage />} />
              <Route path="/help" element={<HelpPage />} />
              <Route path="/meetmap" element={<MeetMapPage />} />
              <Route path="/activity" element={<ActivitySurfPage />} />
              <Route path="/explore" element={<Navigate to="/meetmap" replace />} />

              {/* Redirect /home to root route */}
              <Route path="/home" element={<Navigate to="/" replace />} />

              {/* Protected routes - require authentication and completed onboarding */}
              <Route path="/profile" element={<RequireAuth><RequireOnboarding><ProfilePage /></RequireOnboarding></RequireAuth>} />
              <Route path="/profile/:userId" element={<RequireAuth><RequireOnboarding><ProfilePage /></RequireOnboarding></RequireAuth>} />
              <Route path="/settings" element={<RequireAuth><RequireOnboarding><SettingsPage /></RequireOnboarding></RequireAuth>} />
              <Route path="/meetup" element={<RequireAuth><RequireOnboarding><MeetupPage /></RequireOnboarding></RequireAuth>} />
              <Route path="/earn" element={<RequireAuth><RequireOnboarding><EarnPage /></RequireOnboarding></RequireAuth>} />
              <Route path="/notifications" element={<RequireAuth><RequireOnboarding><NotificationsPage /></RequireOnboarding></RequireAuth>} />
              <Route path="/activity/create" element={<RequireAuth><RequireOnboarding><CreateActivityPage /></RequireOnboarding></RequireAuth>} />
              <Route path="/activity/:id" element={<RequireAuth><RequireOnboarding><ActivityPage /></RequireOnboarding></RequireAuth>} />
              <Route path="/hire" element={<RequireAuth><RequireOnboarding><HirePage /></RequireOnboarding></RequireAuth>} />
              <Route path="/chat" element={<RequireAuth><RequireOnboarding><ChatPage /></RequireOnboarding></RequireAuth>} />
              <Route path="/wallet" element={<RequireAuth><RequireOnboarding><WalletPage /></RequireOnboarding></RequireAuth>} />
              <Route path="/admin" element={<RequireAuth><RequireOnboarding><AdminPage /></RequireOnboarding></RequireAuth>} />
              <Route path="/subscription" element={<RequireAuth><RequireOnboarding><SubscriptionPage /></RequireOnboarding></RequireAuth>} />
              <Route path="/network/:type/:userId" element={<RequireAuth><RequireOnboarding><NetworkPage /></RequireOnboarding></RequireAuth>} />
              <Route path="/gigs" element={<RequireAuth><RequireOnboarding><GigsPage /></RequireOnboarding></RequireAuth>} />
              <Route path="/date-picker-test" element={<DatePickerTest />} />

              {/* Username-based profile route - public to allow profile sharing */}
              <Route path="/:username" element={<ProfilePage />} />

              {/* Removed /subscription-demo route */}
            </Routes>
            {/* Render OnboardingController inside the Router context */}
            <OnboardingController />
            <Toaster />
          </Router>
        </AuthProvider>
      </QueryClientProvider>
    </ThemeProvider>
  );
}

export default App;
