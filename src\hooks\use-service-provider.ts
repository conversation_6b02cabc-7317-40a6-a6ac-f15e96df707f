
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

interface ServiceProvider {
  id: string;
  name: string;
  bio: string;
  hourlyRate: number;
  rating: number;
  totalReviews: number;
  tags: string[];
  location: string;
  avatarUrl: string;
  isVerified: boolean;
  verificationStatus: string;
}

export function useServiceProvider(providerId: string) {
  return useQuery({
    queryKey: ['service-provider', providerId],
    queryFn: async (): Promise<ServiceProvider | null> => {
      if (!providerId) return null;

      // Return mock data for now since we don't have service_providers table
      return {
        id: providerId,
        name: '<PERSON>',
        bio: 'Professional service provider',
        hourlyRate: 50,
        rating: 4.5,
        totalReviews: 10,
        tags: ['professional'],
        location: 'San Francisco, CA',
        avatarUrl: '',
        isVerified: true,
        verificationStatus: 'verified'
      };
    },
    enabled: !!providerId
  });
}

export function useServiceCategories() {
  return useQuery({
    queryKey: ['service-categories'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .eq('type', 'service')
        .order('name');

      if (error) throw error;
      return data || [];
    }
  });
}
