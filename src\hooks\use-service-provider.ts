
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/use-auth';
import { useToast } from '@/hooks/use-toast';

export interface ServiceProvider {
  id: string;
  user_id: string;
  business_name: string;
  description?: string;
  hourly_rate?: number;
  location?: string;
  service_area_radius: number;
  tags: string[];
  is_verified: boolean;
  verification_level_id?: string;
  verification_status: 'pending' | 'verified' | 'rejected' | 'suspended';
  trust_score: number;
  total_reviews: number;
  average_rating: number;
  total_completed_jobs: number;
  response_time_hours: number;
  availability_schedule: any;
  portfolio_images: string[];
  certifications: any[];
  insurance_info: any;
  business_documents: any;
  created_at: string;
  updated_at: string;
  user?: {
    display_name?: string;
    avatar_url?: string;
    username?: string;
    bio?: string;
  };
  services?: Service[];
}

export interface Service {
  id: string;
  provider_id: string;
  category_id?: string;
  title: string;
  description: string;
  price_type: 'fixed' | 'hourly' | 'custom';
  base_price?: number;
  hourly_rate?: number;
  estimated_duration_hours?: number;
  service_location: 'client_location' | 'provider_location' | 'remote' | 'flexible';
  max_travel_distance: number;
  requirements?: string;
  what_included?: string;
  add_ons: any[];
  is_active: boolean;
  is_featured: boolean;
  booking_advance_notice_hours: number;
  cancellation_policy?: string;
  gallery_images: string[];
  faq: any[];
  created_at: string;
  updated_at: string;
  category?: {
    name: string;
    icon?: string;
  };
}

export function useServiceProviders(categoryId?: string, searchQuery?: string) {
  return useQuery({
    queryKey: ['service-providers', categoryId, searchQuery],
    queryFn: async (): Promise<ServiceProvider[]> => {
      let query = supabase
        .from('service_providers')
        .select(`
          *,
          user:profiles!service_providers_user_id_fkey (
            display_name,
            avatar_url,
            username,
            bio
          ),
          services:services!services_provider_id_fkey (
            id,
            title,
            description,
            price_type,
            base_price,
            hourly_rate,
            is_active,
            category:service_categories!services_category_id_fkey (
              name,
              icon
            )
          )
        `)
        .eq('verification_status', 'verified')
        .order('average_rating', { ascending: false });

      if (searchQuery) {
        query = query.or(`business_name.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%,tags.cs.{${searchQuery}}`);
      }

      const { data, error } = await query;

      if (error) throw error;

      let filteredData = data || [];

      // Filter by category if specified
      if (categoryId) {
        filteredData = filteredData.filter(provider => 
          provider.services?.some(service => service.category_id === categoryId)
        );
      }

      return filteredData.map(provider => ({
        ...provider,
        user: provider.user ? {
          display_name: provider.user.display_name || '',
          avatar_url: provider.user.avatar_url || '',
          username: provider.user.username || '',
          bio: provider.user.bio || ''
        } : undefined,
        services: (provider.services || []).map(service => ({
          ...service,
          category: service.category ? {
            name: service.category.name || '',
            icon: service.category.icon || ''
          } : undefined
        }))
      })) as ServiceProvider[];
    }
  });
}

export function useUserServiceProvider() {
  const { user } = useAuth();

  return useQuery({
    queryKey: ['user-service-provider', user?.id],
    queryFn: async (): Promise<ServiceProvider | null> => {
      if (!user) return null;

      const { data, error } = await supabase
        .from('service_providers')
        .select(`
          *,
          user:profiles!service_providers_user_id_fkey (
            display_name,
            avatar_url,
            username,
            bio
          ),
          services:services!services_provider_id_fkey (
            *,
            category:service_categories!services_category_id_fkey (
              name,
              icon
            )
          )
        `)
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      if (!data) return null;

      return {
        ...data,
        user: data.user ? {
          display_name: data.user.display_name || '',
          avatar_url: data.user.avatar_url || '',
          username: data.user.username || '',
          bio: data.user.bio || ''
        } : undefined,
        services: (data.services || []).map(service => ({
          ...service,
          category: service.category ? {
            name: service.category.name || '',
            icon: service.category.icon || ''
          } : undefined
        }))
      } as ServiceProvider;
    },
    enabled: !!user
  });
}

export function useCreateServiceProvider() {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (providerData: Partial<ServiceProvider>) => {
      if (!user) throw new Error('You must be logged in to become a service provider');

      const { data, error } = await supabase
        .from('service_providers')
        .insert({
          ...providerData,
          user_id: user.id
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['service-providers'] });
      queryClient.invalidateQueries({ queryKey: ['user-service-provider'] });
      toast({
        title: 'Success!',
        description: 'You are now registered as a service provider',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive'
      });
    }
  });
}

export function useServiceCategories() {
  return useQuery({
    queryKey: ['service-categories'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('service_categories')
        .select('*')
        .eq('is_active', true)
        .order('sort_order', { ascending: true });

      if (error) throw error;
      return data || [];
    }
  });
}
