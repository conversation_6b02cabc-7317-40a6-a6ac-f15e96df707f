
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/use-auth';
import { useToast } from '@/hooks/use-toast';

interface ServiceProvider {
  id: string;
  name: string;
  bio: string;
  hourlyRate: number;
  rating: number;
  totalReviews: number;
  tags: string[];
  location: string;
  avatarUrl: string;
  isVerified: boolean;
  verificationStatus: string;
}

export function useServiceProvider(providerId: string) {
  return useQuery({
    queryKey: ['service-provider', providerId],
    queryFn: async (): Promise<ServiceProvider | null> => {
      if (!providerId) return null;

      // Return mock data for now since we don't have service_providers table
      return {
        id: providerId,
        name: '<PERSON>',
        bio: 'Professional service provider',
        hourlyRate: 50,
        rating: 4.5,
        totalReviews: 10,
        tags: ['professional'],
        location: 'San Francisco, CA',
        avatarUrl: '',
        isVerified: true,
        verificationStatus: 'verified'
      };
    },
    enabled: !!providerId
  });
}

export function useServiceCategories() {
  return useQuery({
    queryKey: ['service-categories'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .eq('type', 'service')
        .order('name');

      if (error) throw error;
      return data || [];
    }
  });
}

export function useUserServiceProvider() {
  const { user } = useAuth();

  return useQuery({
    queryKey: ['user-service-provider', user?.id],
    queryFn: async (): Promise<ServiceProvider | null> => {
      if (!user) return null;

      // Return mock data for now since we don't have service_providers table
      return {
        id: user.id,
        name: 'Current User Provider',
        bio: 'Your service provider profile',
        hourlyRate: 25,
        rating: 0,
        totalReviews: 0,
        tags: [],
        location: 'Your Location',
        avatarUrl: '',
        isVerified: false,
        verificationStatus: 'pending'
      };
    },
    enabled: !!user
  });
}

export function useCreateServiceProvider() {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: {
      business_name: string;
      description: string;
      hourly_rate: number;
      location: string;
      tags: string[];
      service_area_radius: number;
      availability_schedule: any;
      portfolio_images: string[];
      certifications: string[];
      insurance_info: any;
      business_documents: any;
    }) => {
      if (!user) throw new Error('User not authenticated');

      // Mock implementation for now
      console.log('Creating service provider with data:', data);

      return {
        id: crypto.randomUUID(),
        user_id: user.id,
        name: data.business_name,
        bio: data.description,
        hourly_rate: data.hourly_rate,
        location: data.location,
        tags: data.tags,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-service-provider'] });
      toast({
        title: 'Success!',
        description: 'Service provider profile created successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to create service provider profile',
        variant: 'destructive'
      });
    }
  });
}
