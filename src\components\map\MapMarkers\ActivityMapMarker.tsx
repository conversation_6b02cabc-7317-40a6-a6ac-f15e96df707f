
import React from 'react';
import { Activity } from '@/hooks/use-activities';
import { cn } from '@/lib/utils';
import {
  Calendar,
  Music,
  Coffee,
  BookOpen,
  Map,
  Users,
  Palette,
  Utensils,
  CircleDollarSign,
  CircleDot
} from 'lucide-react';

interface ActivityMapMarkerProps {
  activity: Activity;
  isSelected?: boolean;
  isNew?: boolean;
  onClick?: () => void;
  size?: 'sm' | 'md' | 'lg';
}

export function ActivityMapMarker({
  activity,
  isSelected = false,
  isNew = false,
  onClick,
  size = 'md'
}: ActivityMapMarkerProps) {
  // Size classes
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-12 h-12'
  };
  
  const iconSizes = {
    sm: 16,
    md: 18,
    lg: 22
  };
  
  // Get color based on category
  const getCategoryColor = () => {
    const categoryName = activity.category?.name || '';
    const categoryColors: Record<string, string> = {
      'Sports': 'bg-blue-600',
      'Social': 'bg-violet-500',
      'Food': 'bg-orange-500',
      'Education': 'bg-emerald-500',
      'Arts': 'bg-pink-500',
      'Music': 'bg-indigo-500',
      'Outdoors': 'bg-lime-600',
      'Games': 'bg-fuchsia-500',
      'Technology': 'bg-sky-600',
      'Wellness': 'bg-teal-500',
      'Business': 'bg-slate-600',
    };
    
    return categoryColors[categoryName] || 'bg-gray-500';
  };
  
  // Get icon based on category
  const getCategoryIcon = () => {
    const categoryName = activity.category?.name || '';
    const categoryIcons: Record<string, JSX.Element> = {
      'Sports': <Map size={iconSizes[size]} />,
      'Social': <Users size={iconSizes[size]} />,
      'Food': <Utensils size={iconSizes[size]} />,
      'Education': <BookOpen size={iconSizes[size]} />,
      'Arts': <Palette size={iconSizes[size]} />,
      'Music': <Music size={iconSizes[size]} />,
      'Outdoors': <Map size={iconSizes[size]} />,
      'Wellness': <Coffee size={iconSizes[size]} />
    };
    
    return categoryIcons[categoryName] || <Calendar size={iconSizes[size]} />;
  };
  
  return (
    <div 
      className="relative group cursor-pointer"
      onClick={onClick}
    >
      {/* Pulse effect for new activities */}
      {isNew && (
        <div className="absolute inset-0 rounded-lg animate-ping bg-primary/30" />
      )}
      
      {/* Activity marker */}
      <div
        className={cn(
          sizeClasses[size],
          getCategoryColor(),
          'flex items-center justify-center text-white rounded-lg transition-all duration-300',
          isSelected ? 'shadow-lg ring-2 ring-primary ring-offset-2' : 'shadow',
          'group-hover:scale-110 group-hover:shadow-lg'
        )}
      >
        {getCategoryIcon()}
      </div>
      
      {/* Price badge for paid activities */}
      {activity.is_paid && activity.price && (
        <div className="absolute -top-2 -right-2 min-w-5 h-5 bg-green-500 rounded-full border border-white flex items-center justify-center px-1 text-xs text-white font-bold shadow-sm">
          ${Math.round(Number(activity.price))}
        </div>
      )}
    </div>
  );
}
