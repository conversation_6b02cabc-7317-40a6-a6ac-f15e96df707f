
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { useActivityView } from '@/hooks/use-activity-view';
import { useActivities } from '@/hooks/use-activities';
import { EnhancedActivityDetailsModal } from './EnhancedActivityDetailsModal';

export function useActivityRouter() {
  const { selectedActivity, setSelectedActivity } = useActivityView();
  const { activities } = useActivities();

  const openModal = (activity: any) => {
    setSelectedActivity(activity);
  };

  const closeModal = () => {
    setSelectedActivity(null);
  };

  const ActivityModal = () => {
    if (!selectedActivity) return null;

    return (
      <Dialog open={!!selectedActivity} onOpenChange={closeModal}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto p-0">
          <EnhancedActivityDetailsModal 
            activity={selectedActivity}
            onClose={closeModal}
          />
        </DialogContent>
      </Dialog>
    );
  };

  return {
    openModal,
    closeModal,
    ActivityModal,
    selectedActivity,
    activities: activities || []
  };
}

export function ActivityRouter() {
  const { ActivityModal } = useActivityRouter();
  return <ActivityModal />;
}
