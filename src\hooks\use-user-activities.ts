
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Activity } from '@/types/activity';
import { useAuth } from './use-auth';
import { toUnifiedLocation } from '@/utils/location-normalizer';

export interface UseUserActivitiesOptions {
  limit?: number;
  filter?: 'hosting' | 'participating' | 'all';
  includeCompleted?: boolean;
  includeCancelled?: boolean;
}

export function useUserActivities(options: UseUserActivitiesOptions = {}) {
  const { 
    limit = 20, 
    filter = 'all', 
    includeCompleted = false,
    includeCancelled = false
  } = options;
  const { user } = useAuth();

  const query = useQuery({
    queryKey: ['user-activities', user?.id, limit, filter, includeCompleted, includeCancelled],
    queryFn: async () => {
      if (!user?.id) return [];

      try {
        let query = supabase
          .from('activities')
          .select(`
            *,
            host:host_id (
              id, 
              display_name,
              username,
              avatar_url
            ),
            category:category_id (
              id,
              name,
              icon,
              type
            ),
            current_participants:activity_participants!activity_id(count)
          `);

        // Filter based on user role
        if (filter === 'hosting') {
          query = query.eq('host_id', user.id);
        } else if (filter === 'participating') {
          // Use a subquery to get activity IDs where the user is participating
          const { data: participatingActivities } = await supabase
            .from('activity_participants')
            .select('activity_id')
            .eq('user_id', user.id)
            .eq('status', 'confirmed');
          
          // If we have participating activities, filter by those IDs
          if (participatingActivities && participatingActivities.length > 0) {
            const activityIds = participatingActivities.map(item => item.activity_id);
            query = query.in('id', activityIds);
          } else {
            // No participating activities found
            return [];
          }
        }

        // Filter by status
        const statusFilters: string[] = ['active'];
        if (includeCompleted) statusFilters.push('completed');
        if (includeCancelled) statusFilters.push('cancelled');
        query = query.in('status', statusFilters);

        // Order and limit
        query = query.order('start_time', { ascending: true }).limit(limit);

        const { data, error } = await query;
        
        if (error) throw error;

        // Map the data and normalize the activity format
        return data.map((activity: any): Activity => {
          // Calculate current participants count from the sub-query result
          const participantCount = Array.isArray(activity.current_participants) 
            ? activity.current_participants.length 
            : 0;

          // Convert location to unified format
          const location = toUnifiedLocation(activity.location);

          return {
            id: activity.id,
            title: activity.title,
            description: activity.description,
            start_time: activity.start_time,
            end_time: activity.end_time,
            location: location,
            address: activity.address,
            host_id: activity.host_id,
            host: activity.host || undefined,
            category: activity.category || undefined,
            is_paid: activity.is_paid || false,
            price: activity.price,
            max_participants: activity.max_participants,
            current_participants: participantCount,
            media_urls: activity.media_urls,
            created_at: activity.created_at,
            updated_at: activity.updated_at,
            status: activity.status as 'active' | 'cancelled' | 'completed',
            visibility: activity.visibility,
            queue_type: activity.queue_type,
            allow_waitlist: activity.allow_waitlist,
            group_chat_id: activity.group_chat_id,
            moderation_status: activity.moderation_status || '',
            is_verified: activity.is_verified || false
          };
        });
      } catch (error) {
        console.error('Error fetching user activities:', error);
        throw error;
      }
    },
    enabled: !!user?.id,
  });

  return {
    data: query.data || [],
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    refetch: query.refetch
  };
}
