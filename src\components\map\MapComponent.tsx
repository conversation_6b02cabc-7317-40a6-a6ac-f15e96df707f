
import React, { useEffect, useRef, useState } from 'react';
import mapboxgl from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';
import { NavigationControl, GeolocateControl } from 'mapbox-gl';
import { Activity } from '@/hooks/use-activities';
import { UnifiedMapUser } from '@/types/map';
import { EnhancedMarkerManager } from './MapMarkers/EnhancedMarkerManager';
import { UserLocationMarker } from './MapMarkers/UserLocationMarker';
import { cn } from '@/lib/utils';
import MapRightSidebar from './MapRightSidebar/MapRightSidebar';
import { ActivityDetailsModal } from './ActivityDetailsModal';
import { useMapStyle } from '@/hooks/use-map-style';

interface MapComponentProps {
  mapboxToken: string;
  userLocations: UnifiedMapUser[];
  onMarkerClick: (user: UnifiedMapUser) => void;
  onActivityClick?: (activity: Activity) => void;
  isLoading?: boolean;
  showUsers?: boolean;
  showActivities?: boolean;
  activeFilter?: 'all' | 'people' | 'activities';
  mapRef?: React.MutableRefObject<mapboxgl.Map | null>;
  mapStyle?: string;
  onMapStyleChange?: (style: string) => void;
  enablePitch?: boolean;
  enableRotation?: boolean;
  showBuildings?: boolean;
  initialLocation?: { lng: number; lat: number } | null;
  isLocationLoading?: boolean;
  showSavedLocations?: boolean;
  showRightSidebar?: boolean;
  activities?: Activity[];
}

export function MapComponent({
  mapboxToken,
  userLocations,
  onMarkerClick,
  onActivityClick,
  isLoading = false,
  showUsers = true,
  showActivities = true,
  activeFilter = 'all',
  mapRef: externalMapRef,
  mapStyle = 'streets-v11',
  onMapStyleChange,
  enablePitch = true,
  enableRotation = true,
  showBuildings = true,
  initialLocation,
  isLocationLoading = false,
  showSavedLocations = true,
  showRightSidebar = true,
  activities = []
}: MapComponentProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<mapboxgl.Map | null>(null);
  const [mapLoaded, setMapLoaded] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UnifiedMapUser | null>(null);
  const [selectedActivity, setSelectedActivity] = useState<Activity | null>(null);
  const [isActivityModalOpen, setIsActivityModalOpen] = useState(false);

  const { styleConfig, isCustomStyle } = useMapStyle();

  useEffect(() => {
    if (!containerRef.current || mapInstanceRef.current) return;

    mapboxgl.accessToken = mapboxToken;
    
    const map = new mapboxgl.Map({
      container: containerRef.current,
      style: `mapbox://styles/mapbox/${mapStyle}`,
      center: initialLocation 
        ? [initialLocation.lng, initialLocation.lat]
        : [-122.4, 37.8],
      zoom: 14,
      pitch: enablePitch ? 45 : 0,
      bearing: 0,
      antialias: true
    });

    map.on('load', () => {
      setMapLoaded(true);
      
      map.addControl(new NavigationControl(), 'top-right');
      map.addControl(
        new GeolocateControl({
          positionOptions: { enableHighAccuracy: true },
          trackUserLocation: true,
          showUserHeading: true
        }),
        'top-right'
      );

      if (styleConfig?.fog) {
        map.setFog(styleConfig.fog);
      }
      
      if (styleConfig?.light) {
        map.setLight(styleConfig.light);
      }

      if (showBuildings) {
        const buildingColor = styleConfig?.buildingConfig?.fillColor || '#d8e2ebaa';
        const buildingOpacity = styleConfig?.buildingConfig?.opacity || 0.7;
        
        map.addLayer({
          'id': '3d-buildings',
          'source': 'composite',
          'source-layer': 'building',
          'filter': ['==', 'extrude', 'true'],
          'type': 'fill-extrusion',
          'minzoom': 14,
          'paint': {
            'fill-extrusion-color': [
              'interpolate',
              ['linear'],
              ['get', 'height'],
              0, buildingColor,
              50, '#c3d5e4aa',
              100, '#a7c6daaa',
              200, '#86b0ccaa',
              400, '#6699bbaa'
            ],
            'fill-extrusion-height': ['get', 'height'],
            'fill-extrusion-base': ['get', 'min_height'],
            'fill-extrusion-opacity': buildingOpacity,
            'fill-extrusion-vertical-gradient': true
          }
        });

        if (styleConfig?.vegetationConfig) {
          try {
            map.addLayer({
              'id': 'vegetation-layer',
              'type': 'fill',
              'source': 'composite',
              'source-layer': 'landuse',
              'filter': [
                'in', 
                'class', 
                'park', 'wood', 'grass', 'forest', 'garden'
              ],
              'paint': {
                'fill-color': styleConfig.vegetationConfig.fillColor,
                'fill-opacity': styleConfig.vegetationConfig.opacity
              }
            }, '3d-buildings');
          } catch (error) {
            console.log('Could not add vegetation layer, continuing without it');
          }
        }
      }
    });

    mapInstanceRef.current = map;
    if (externalMapRef) {
      externalMapRef.current = map;
    }

    return () => {
      map.remove();
      mapInstanceRef.current = null;
      if (externalMapRef) {
        externalMapRef.current = null;
      }
    };
  }, [mapboxToken, mapStyle, enablePitch, showBuildings, initialLocation, styleConfig]);

  const handleMarkerClick = (user: UnifiedMapUser) => {
    setSelectedUser(user);
    setSelectedActivity(null);
    onMarkerClick(user);
  };

  const handleActivityClick = (activity: Activity) => {
    if (!activity) return;
    
    setSelectedActivity(activity);
    setSelectedUser(null);
    setIsActivityModalOpen(true);
    onActivityClick?.(activity);
  };

  return (
    <div className="relative w-full h-full flex">
      <div className="relative flex-1">
        <div ref={containerRef} className="absolute inset-0" />
        
        {!mapLoaded && (
          <div className="absolute inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm">
            <div className="flex flex-col items-center gap-2">
              <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent" />
              <p className="text-sm font-medium">Loading map...</p>
            </div>
          </div>
        )}

        {mapInstanceRef.current && mapLoaded && (
          <>
            <UserLocationMarker map={mapInstanceRef.current} />
            <EnhancedMarkerManager
              map={mapInstanceRef.current}
              users={userLocations}
              activities={activities || []}
              selectedUser={selectedUser}
              selectedActivity={selectedActivity}
              onUserClick={handleMarkerClick}
              onActivityClick={handleActivityClick}
              showUsers={showUsers}
              showActivities={showActivities}
            />
          </>
        )}
      </div>

      {showRightSidebar && (
        <MapRightSidebar
          users={userLocations}
          activities={activities}
          onUserSelect={handleMarkerClick}
          onActivitySelect={handleActivityClick}
          className="h-full"
          activeTab="nearby"
          onTabChange={() => {}}
        />
      )}
      
      <ActivityDetailsModal
        activity={selectedActivity}
        isOpen={isActivityModalOpen}
        onClose={() => setIsActivityModalOpen(false)}
      />
    </div>
  );
}
