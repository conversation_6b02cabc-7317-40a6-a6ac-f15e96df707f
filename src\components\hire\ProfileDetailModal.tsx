import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Calendar } from "@/components/ui/calendar";
import { ScrollArea } from "@/components/ui/scroll-area";
import { FriendProfile, Review } from "@/types/hire";

interface ProfileDetailModalProps {
  profile: FriendProfile;
  isOpen: boolean;
  onClose: () => void;
}

const ProfileDetailModal: React.FC<ProfileDetailModalProps> = ({
  profile,
  isOpen,
  onClose,
}) => {
  // Mock reviews data
  const reviews: Review[] = [
    {
      id: "1",
      reviewerName: "<PERSON>",
      rating: 5,
      text: "Sarah was an amazing guide! She showed me all the hidden gems in the city.",
      date: "2024-03-15",
    },
    {
      id: "2",
      reviewerName: "<PERSON>",
      rating: 4,
      text: "Great company and very knowledgeable about local history.",
      date: "2024-03-10",
    },
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Profile Details</DialogTitle>
        </DialogHeader>

        <div className="flex gap-6">
          {/* Left Column - Profile Info */}
          <div className="flex-1">
            <div className="flex items-start gap-4">
              <Avatar className="h-20 w-20">
                <AvatarImage src={profile.avatarUrl} />
                <AvatarFallback>{profile.displayName[0]}</AvatarFallback>
              </Avatar>
              <div>
                <h2 className="text-2xl font-bold">{profile.displayName}</h2>
                <p className="text-gray-500">{profile.username}</p>
                <div className="flex items-center gap-1 mt-1">
                  <span className="text-yellow-400">⭐</span>
                  <span>{profile.rating}</span>
                  <span className="text-gray-500">({profile.reviewCount} reviews)</span>
                </div>
              </div>
            </div>

            <div className="mt-6">
              <h3 className="font-semibold mb-2">About</h3>
              <p className="text-gray-600">{profile.bio}</p>
            </div>

            <div className="mt-6">
              <h3 className="font-semibold mb-2">Vibes & Interests</h3>
              <div className="flex flex-wrap gap-2">
                {profile.vibes.map((vibe) => (
                  <Badge key={vibe} variant="secondary">{vibe}</Badge>
                ))}
              </div>
            </div>

            <div className="mt-6">
              <h3 className="font-semibold mb-2">Available For</h3>
              <div className="flex flex-wrap gap-2">
                {profile.hirePurposes.map((purpose) => (
                  <Badge key={purpose} variant="outline">{purpose}</Badge>
                ))}
              </div>
            </div>

            <div className="mt-6">
              <h3 className="font-semibold mb-2">Location</h3>
              <p className="text-gray-600">{profile.location}</p>
              <p className="text-sm text-gray-500">{profile.distance}km away</p>
            </div>

            <div className="mt-6">
              <h3 className="font-semibold mb-2">Rate</h3>
              <p className="text-xl font-bold">${profile.hourlyRate}/hr</p>
            </div>
          </div>

          {/* Right Column - Tabs */}
          <div className="flex-1">
            <Tabs defaultValue="availability">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="availability">Availability</TabsTrigger>
                <TabsTrigger value="reviews">Reviews</TabsTrigger>
              </TabsList>

              <TabsContent value="availability" className="mt-4">
                <Calendar
                  mode="single"
                  className="rounded-md border"
                />
              </TabsContent>

              <TabsContent value="reviews" className="mt-4">
                <ScrollArea className="h-[400px] pr-4">
                  <div className="space-y-4">
                    {reviews.map((review) => (
                      <div key={review.id} className="border-b pb-4">
                        <div className="flex items-center justify-between mb-2">
                          <span className="font-semibold">{review.reviewerName}</span>
                          <span className="text-sm text-gray-500">{review.date}</span>
                        </div>
                        <div className="flex items-center gap-1 mb-2">
                          <span className="text-yellow-400">⭐</span>
                          <span>{review.rating}</span>
                        </div>
                        <p className="text-gray-600">{review.text}</p>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </TabsContent>
            </Tabs>
          </div>
        </div>

        <div className="flex justify-end gap-4 mt-6">
          <Button variant="outline" onClick={onClose}>Close</Button>
          <Button>Hire Now</Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ProfileDetailModal; 