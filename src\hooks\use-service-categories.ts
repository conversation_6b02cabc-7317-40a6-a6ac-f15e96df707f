
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

interface ServiceCategory {
  id: string;
  name: string;
  icon?: string;
  type?: string;
}

export function useServiceCategories() {
  return useQuery({
    queryKey: ['service-categories'],
    queryFn: async (): Promise<ServiceCategory[]> => {
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .eq('type', 'service')
        .order('name');

      if (error) throw error;

      return (data || []).map(category => ({
        id: category.id,
        name: category.name,
        icon: category.icon,
        type: category.type
      }));
    }
  });
}
