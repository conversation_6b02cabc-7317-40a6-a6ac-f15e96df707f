import React, { useEffect, useRef, useState } from 'react';
import mapboxgl from 'mapbox-gl';
import { Activity } from '@/hooks/use-activities';
import { UnifiedMapUser, calculateHaversineDistance } from '@/types/map';
import { createUserMarker, createActivityMarker } from './createMapMarker';

interface MarkerClusterProps {
  map: mapboxgl.Map | null;
  users: UnifiedMapUser[];
  activities: Activity[];
  showUsers: boolean;
  showActivities: boolean;
  onUserClick: (user: UnifiedMapUser) => void;
  onActivityClick: (activity: Activity) => void;
}

export function MarkerCluster({
  map,
  users,
  activities,
  showUsers,
  showActivities,
  onUserClick,
  onActivityClick
}: MarkerClusterProps) {
  const userMarkersRef = useRef<{ [key: string]: mapboxgl.Marker }>({});
  const activityMarkersRef = useRef<{ [key: string]: mapboxgl.Marker }>({});
  const clusterSourceId = useRef('clusters');
  const [selectedUser, setSelectedUser] = useState<string | null>(null);
  const [selectedActivity, setSelectedActivity] = useState<string | null>(null);

  useEffect(() => {
    if (!map || !showUsers) {
      Object.values(userMarkersRef.current).forEach(marker => marker.remove());
      userMarkersRef.current = {};
      return;
    }

    Object.entries(userMarkersRef.current).forEach(([userId, marker]) => {
      if (!users.some(user => user.user_id === userId)) {
        marker.remove();
        delete userMarkersRef.current[userId];
      }
    });

    users.forEach(user => {
      if (!user.location) return;
      
      const handleUserClick = () => {
        setSelectedUser(user.user_id);
        setSelectedActivity(null);
        onUserClick(user);
      };

      if (userMarkersRef.current[user.user_id]) {
        userMarkersRef.current[user.user_id].setLngLat([user.location.x, user.location.y]);
      } else {
        const marker = createUserMarker(
          user,
          map,
          handleUserClick
        );
        
        if (marker) {
          marker.addTo(map);
          userMarkersRef.current[user.user_id] = marker;
        }
      }
    });

    return () => {
      if (!map) return;
      Object.values(userMarkersRef.current).forEach(marker => marker.remove());
      userMarkersRef.current = {};
    };
  }, [map, users, showUsers, onUserClick]);

  useEffect(() => {
    if (!map || !showActivities) {
      Object.values(activityMarkersRef.current).forEach(marker => marker.remove());
      activityMarkersRef.current = {};
      return;
    }

    Object.entries(activityMarkersRef.current).forEach(([activityId, marker]) => {
      if (!activities.some(activity => activity.id === activityId)) {
        marker.remove();
        delete activityMarkersRef.current[activityId];
      }
    });

    activities.forEach(activity => {
      if (!activity.location) return;
      
      const handleActivityClick = () => {
        setSelectedActivity(activity.id);
        setSelectedUser(null);
        onActivityClick(activity);
      };

      const isSelected = selectedActivity === activity.id;
      const isNew = false;

      if (activityMarkersRef.current[activity.id]) {
        activityMarkersRef.current[activity.id].setLngLat([activity.location.x, activity.location.y]);
      } else {
        const marker = createActivityMarker(
          activity,
          map,
          handleActivityClick,
          isSelected,
          isNew
        );
        
        if (marker) {
          marker.addTo(map);
          activityMarkersRef.current[activity.id] = marker;
        }
      }
    });

    return () => {
      if (!map) return;
      Object.values(activityMarkersRef.current).forEach(marker => marker.remove());
      activityMarkersRef.current = {};
    };
  }, [map, activities, showActivities, onActivityClick, selectedActivity]);

  useEffect(() => {
    if (!map || !map.isStyleLoaded()) return;
    
    if (!map.getSource(clusterSourceId.current)) {
      map.addSource(clusterSourceId.current, {
        type: 'geojson',
        data: {
          type: 'FeatureCollection',
          features: []
        },
        cluster: true,
        clusterMaxZoom: 14,
        clusterRadius: 50
      });
      
      map.addLayer({
        id: 'clusters-circle',
        type: 'circle',
        source: clusterSourceId.current,
        filter: ['has', 'point_count'],
        paint: {
          'circle-color': [
            'step',
            ['get', 'point_count'],
            '#4f46e5',
            5,
            '#0ea5e9',
            15,
            '#6366f1'
          ],
          'circle-radius': [
            'step',
            ['get', 'point_count'],
            20,
            5,
            25,
            15,
            30
          ],
          'circle-opacity': 0.8
        }
      });
      
      map.addLayer({
        id: 'cluster-count',
        type: 'symbol',
        source: clusterSourceId.current,
        filter: ['has', 'point_count'],
        layout: {
          'text-field': '{point_count_abbreviated}',
          'text-size': 12,
          'text-font': ['DIN Offc Pro Medium', 'Arial Unicode MS Bold']
        },
        paint: {
          'text-color': '#ffffff'
        }
      });
      
      map.on('click', 'clusters-circle', (e) => {
        const features = map.queryRenderedFeatures(e.point, { layers: ['clusters-circle'] });
        if (!features.length) return;
        
        const clusterId = features[0].properties?.cluster_id;
        const source = map.getSource(clusterSourceId.current);
        
        if (source && 'getClusterExpansionZoom' in source) {
          source.getClusterExpansionZoom(clusterId, (err, zoom) => {
            if (err) return;
            
            const coordinates = (features[0].geometry as any).coordinates;
            map.easeTo({
              center: coordinates,
              zoom: Math.min(zoom + 1, 18)
            });
          });
        }
      });
      
      map.on('mouseenter', 'clusters-circle', () => {
        map.getCanvas().style.cursor = 'pointer';
      });
      
      map.on('mouseleave', 'clusters-circle', () => {
        map.getCanvas().style.cursor = '';
      });
    }
    
    const updateClusterSource = () => {
      if (!map || !map.getSource(clusterSourceId.current)) return;
      
      const features: any[] = [];
      
      if (showUsers) {
        users.forEach(user => {
          if (!user.location) return;
          
          features.push({
            type: 'Feature',
            properties: {
              id: user.user_id,
              type: 'user',
              title: user.display_name || user.username || 'User',
              avatar: user.avatar_url,
              isOnline: user.is_online
            },
            geometry: {
              type: 'Point',
              coordinates: [user.location.x, user.location.y]
            }
          });
        });
      }
      
      if (showActivities) {
        activities.forEach(activity => {
          if (!activity.location) return;
          
          features.push({
            type: 'Feature',
            properties: {
              id: activity.id,
              type: 'activity',
              title: activity.title,
              category: activity.category?.name,
              isPaid: activity.is_paid
            },
            geometry: {
              type: 'Point',
              coordinates: [activity.location.x, activity.location.y]
            }
          });
        });
      }
      
      const source = map.getSource(clusterSourceId.current);
      if (source && 'setData' in source) {
        source.setData({
          type: 'FeatureCollection',
          features: features
        });
      }
    };
    
    updateClusterSource();
    
    return () => {
      if (map && map.getLayer('cluster-count')) {
        map.removeLayer('cluster-count');
      }
      
      if (map && map.getLayer('clusters-circle')) {
        map.removeLayer('clusters-circle');
      }
      
      if (map && map.getSource(clusterSourceId.current)) {
        map.removeSource(clusterSourceId.current);
      }
    };
  }, [map, users, activities, showUsers, showActivities]);

  return null;
}
