
import React from 'react';
import { Button } from "@/components/ui/button";
import { Activity } from "@/hooks/use-activities";
import { useJoinActivity } from "@/hooks/use-activity-participants";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from '@/hooks/use-auth';
import { cn } from '@/lib/utils';
import { MessageSquare, Loader2 } from 'lucide-react';
import { isPast } from 'date-fns';
import { ActivityChatButton } from './ActivityChatButton';

interface ActivityJoinProps {
  activity: Activity;
  size?: 'sm' | 'default' | 'lg';
  className?: string;
  showChatButton?: boolean;
  onClick?: (e: React.MouseEvent<HTMLButtonElement>) => void;
}

export function ActivityJoin({ activity, size = 'default', className, showChatButton = false, onClick }: ActivityJoinProps) {
  const { toast } = useToast();
  const { user } = useAuth();
  const joinActivity = useJoinActivity();
  
  const isExpired = activity.end_time ? isPast(new Date(activity.end_time)) : false;
  
  const handleJoinClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (onClick) {
      onClick(e);
    }
    
    if (!user) {
      toast({
        title: "Sign in required",
        description: "Please sign in to join this activity",
        variant: "destructive"
      });
      return;
    }
    
    if (isExpired) {
      toast({
        title: "Activity expired",
        description: "This activity has already ended.",
        variant: "destructive"
      });
      return;
    }
    
    joinActivity.mutate({ 
      activityId: activity.id
    });
  };

  return (
    <div className={cn("flex gap-2", showChatButton ? "w-full" : "")}>
      <Button 
        className={cn("font-medium", className)} 
        size={size}
        onClick={handleJoinClick}
        disabled={isExpired || joinActivity.isPending}
      >
        {joinActivity.isPending ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Joining...
          </>
        ) : (
          "Join Activity"
        )}
      </Button>
      
      {showChatButton && (
        <ActivityChatButton 
          activity={activity} 
          size={size}
          variant="outline"
          onClick={(e) => e.stopPropagation()}
        />
      )}
    </div>
  );
}
