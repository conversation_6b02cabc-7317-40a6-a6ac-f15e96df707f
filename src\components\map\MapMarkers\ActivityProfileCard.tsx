
import React, { useState } from 'react';
import { Activity } from '@/hooks/use-activities';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>Footer,
  CardHeader,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Calendar,
  Clock,
  MapPin,
  Users,
  DollarSign,
  MessageCircle,
  Share2,
  X
} from "lucide-react";
import { formatDistanceToNow, format } from 'date-fns';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { UnifiedMapUser } from '@/types/map';
import { useActivityParticipantCount } from '@/hooks/use-activity-participants';
import { ActivityShareModal } from '@/components/activity/ActivityShareModal';

interface ActivityProfileCardProps {
  activity: Activity;
  onClose: () => void;
  onJoin?: () => void;
  onMessage?: () => void;
  host?: UnifiedMapUser;
}

export function ActivityProfileCard({
  activity,
  onClose,
  onJoin,
  onMessage,
  host
}: ActivityProfileCardProps) {
  const { data: participantCount } = useActivityParticipantCount(activity.id);
  const [showShareModal, setShowShareModal] = useState(false);
  
  // Safely handle null or undefined values
  const isFreePricing = !activity.price || activity.price <= 0;
  const hasSpots = !activity.max_participants || 
    (!participantCount || participantCount.confirmed < activity.max_participants);
  
  // Format start time in human-readable format
  const startTimeFormatted = activity.start_time 
    ? format(new Date(activity.start_time), 'h:mm a, MMM d')
    : 'TBD';
    
  // Calculate how much time until the activity starts
  const timeUntil = activity.start_time 
    ? formatDistanceToNow(new Date(activity.start_time), { addSuffix: true })
    : 'Date TBD';
  
  // Determine category icon/emoji
  const categoryEmoji = activity.category?.name 
    ? getCategoryEmoji(activity.category.name)
    : '📌';
  
  // Get participants count
  const participantsCount = participantCount?.confirmed || 0;
  const maxParticipants = activity.max_participants || '∞';

  // Get gradient based on category
  const getGradient = () => {
    const categoryName = activity.category?.name?.toLowerCase();
    if (!categoryName) return 'from-primary/5 via-transparent to-primary/5 border-primary/20';
    
    if (categoryName === 'sports' || categoryName === 'fitness') 
      return 'from-blue-500/5 via-transparent to-blue-500/5 border-blue-200/30';
    if (categoryName === 'music') 
      return 'from-indigo-500/5 via-transparent to-indigo-500/5 border-purple-200/30';
    if (categoryName === 'social' || categoryName === 'dating') 
      return 'from-pink-500/5 via-transparent to-pink-500/5 border-pink-200/30';
    if (categoryName === 'outdoors') 
      return 'from-green-500/5 via-transparent to-green-500/5 border-green-200/30';
    if (categoryName === 'dining') 
      return 'from-amber-500/5 via-transparent to-amber-500/5 border-amber-200/30';
    return 'from-primary/5 via-transparent to-primary/5 border-primary/20'; // default
  };

  const handleShareClick = () => {
    setShowShareModal(true);
  };
  
  return (
    <>
      <Card className="w-80 shadow-lg animate-in fade-in zoom-in duration-300 bg-background/95 backdrop-blur-sm bg-gradient-to-br from-white/40 to-white/20 border-white/10">
        <CardHeader className="pb-2 relative">
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-2 top-2 h-7 w-7 hover:bg-black/5 rounded-full"
            onClick={onClose}
          >
            <X size={16} />
          </Button>
          
          <div className="flex items-start gap-3">
            <div className={`h-16 w-16 flex items-center justify-center rounded-full bg-gradient-to-br ${getGradient()} text-3xl shadow-sm border border-white/10 backdrop-blur-sm`}>
              {categoryEmoji}
            </div>
            
            <div className="flex-1 pt-1">
              <h3 className="font-semibold text-lg truncate max-w-[160px]">
                {activity.title}
              </h3>
              
              <div className="flex items-center text-sm text-muted-foreground mt-0.5">
                <div className="flex items-center gap-1">
                  <Clock className="h-3 w-3 text-primary" />
                  <span>{timeUntil}</span>
                </div>
              </div>
              
              {activity.location && (
                <div className="flex items-center text-sm text-muted-foreground mt-0.5 truncate">
                  <MapPin className="h-3 w-3 mr-1 flex-shrink-0 text-rose-500" />
                  <span className="truncate">{activity.address || 'Location set on map'}</span>
                </div>
              )}
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="pb-3">
          {activity.description && (
            <div className="text-sm mt-1">
              <p className="line-clamp-3">{activity.description || 'No description provided'}</p>
            </div>
          )}
          
          {host && (
            <div className="flex items-center mt-3">
              <Avatar className="h-6 w-6 mr-2 ring-1 ring-primary/10">
                <AvatarImage src={host.avatar_url} />
                <AvatarFallback className="bg-primary/10">{getInitials(host.display_name || host.username || 'Host')}</AvatarFallback>
              </Avatar>
              <span className="text-xs">
                Hosted by <span className="font-medium">{host.display_name || host.username || 'Host'}</span>
              </span>
            </div>
          )}
          
          <div className="flex flex-wrap justify-between mt-3 gap-1">
            <div className="flex items-center text-xs px-2 py-1 rounded-full bg-background/50 backdrop-blur-sm border border-white/10">
              <Calendar className="h-3.5 w-3.5 mr-1 text-primary" />
              <span>{startTimeFormatted}</span>
            </div>
            
            <div className="flex items-center text-xs px-2 py-1 rounded-full bg-background/50 backdrop-blur-sm border border-white/10">
              <Users className="h-3.5 w-3.5 mr-1 text-primary" />
              <span>{participantsCount}/{maxParticipants} joined</span>
            </div>
            
            <Badge variant={isFreePricing ? "outline" : "default"} className={`mt-2 text-xs ${isFreePricing ? 'bg-green-50/50 text-green-700 border-green-200/30' : 'bg-amber-50/50 text-amber-700 border-amber-200/30'} backdrop-blur-sm`}>
              {isFreePricing ? (
                'Free'
              ) : (
                <div className="flex items-center">
                  <DollarSign className="h-3 w-3 mr-0.5" />
                  {typeof activity.price === 'number' ? activity.price.toFixed(2) : activity.price}
                </div>
              )}
            </Badge>
            
            {activity.category && (
              <Badge variant="outline" className="mt-2 text-xs bg-background/50 backdrop-blur-sm border-white/10">
                {activity.category.name}
              </Badge>
            )}
          </div>
        </CardContent>
        
        <CardFooter className="flex justify-between pt-0 gap-2">
          <Button
            size="sm"
            variant="outline"
            className="flex items-center gap-1 flex-1 backdrop-blur-sm border-white/10 bg-background/30 hover:bg-background/50"
            onClick={onMessage || (() => {})}
          >
            <MessageCircle className="h-4 w-4" />
            <span>Message</span>
          </Button>
          <Button
            size="sm"
            className="flex items-center gap-1 flex-1 bg-gradient-to-r from-primary to-primary-purple shadow-sm hover:opacity-90 transition-opacity"
            onClick={onJoin || (() => {})}
            disabled={!hasSpots}
          >
            <Users className="h-4 w-4" />
            <span>{hasSpots ? 'Join' : 'Full'}</span>
          </Button>
        </CardFooter>
        
        <div className="px-6 pb-3 text-center">
          <Button 
            variant="ghost" 
            size="sm" 
            className="w-full text-xs text-primary flex items-center justify-center gap-1 hover:bg-primary/5"
            onClick={handleShareClick}
          >
            <Share2 className="h-3 w-3" />
            <span>Share Activity</span>
          </Button>
        </div>
      </Card>
      
      {showShareModal && (
        <ActivityShareModal
          activity={activity}
          isOpen={showShareModal}
          onClose={() => setShowShareModal(false)}
        />
      )}
    </>
  );
}

// Helper function to generate initials from a name
function getInitials(name: string): string {
  return name
    .split(' ')
    .map(part => part[0])
    .join('')
    .toUpperCase()
    .substring(0, 2);
}

// Helper function to get emoji for activity category
function getCategoryEmoji(category: string): string {
  const categoryEmojis: Record<string, string> = {
    sports: '🏀',
    dining: '🍽️',
    music: '🎵',
    outdoors: '🌳',
    art: '🎨',
    technology: '💻',
    gaming: '🎮',
    education: '📚',
    fitness: '💪',
    social: '🎉',
    volunteering: '🤝',
    dating: '❤️',
    professional: '💼',
    entertainment: '🎬',
    travel: '✈️',
    shopping: '🛍️',
    wellness: '🧘‍♀️',
    default: '📌'
  };
  
  return categoryEmojis[category.toLowerCase()] || categoryEmojis.default;
}
