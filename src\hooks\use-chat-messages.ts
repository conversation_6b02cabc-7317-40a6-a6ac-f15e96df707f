
import { useEffect, useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/use-auth';
import { ChatMessage } from '@/types/chat';

export interface SendMessageInput {
  content: string;
  conversationId?: string;
  attachment_urls?: string[];
  metadata?: Record<string, any>;
}

export function useChatMessages(conversationId?: string) {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  const { data: messages, isLoading } = useQuery({
    queryKey: ['messages', conversationId],
    queryFn: async (): Promise<ChatMessage[]> => {
      if (!conversationId) return [];

      const { data, error } = await supabase
        .from('messages')
        .select(`
          *,
          sender:profiles!messages_sender_id_fkey (
            id,
            display_name,
            avatar_url,
            username
          )
        `)
        .eq('conversation_id', conversationId)
        .order('created_at', { ascending: true });

      if (error) throw error;

      return (data || []).map(msg => ({
        ...msg,
        sender: msg.sender ? {
          id: msg.sender.id || '',
          display_name: msg.sender.display_name || '',
          avatar_url: msg.sender.avatar_url || '',
          username: msg.sender.username || ''
        } : null
      })) as ChatMessage[];
    },
    enabled: !!conversationId
  });

  // Real-time subscription for new messages
  useEffect(() => {
    if (!conversationId) return;

    const channel = supabase
      .channel(`messages:${conversationId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: `conversation_id=eq.${conversationId}`
        },
        () => {
          queryClient.invalidateQueries({
            queryKey: ['messages', conversationId]
          });
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [conversationId, queryClient]);

  const sendMessage = useMutation({
    mutationFn: async (input: SendMessageInput | string) => {
      if (!user || !conversationId) {
        throw new Error("Missing required information to send message");
      }

      const content = typeof input === 'string' ? input : input.content;
      const metadata = typeof input === 'object' ? input.metadata : undefined;

      const { data, error } = await supabase
        .from('messages')
        .insert({
          sender_id: user.id,
          conversation_id: conversationId,
          content: content.trim(),
          message_type: 'text',
          metadata
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['messages', conversationId]
      });
    }
  });

  return {
    data: messages,
    isLoading,
    sendMessage: sendMessage.mutate
  };
}
