
import { useEffect, useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/use-auth';
import { ChatMessage } from '@/types/chat';

export interface SendMessageInput {
  content: string;
  conversationId?: string;
  attachment_urls?: string[];
  metadata?: Record<string, any>;
}

export function useChatMessages(conversationId?: string) {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  const { data: messages, isLoading } = useQuery({
    queryKey: ['messages', conversationId],
    queryFn: async (): Promise<ChatMessage[]> => {
      if (!conversationId) return [];

      const { data, error } = await supabase
        .from('messages')
        .select('*')
        .eq('conversation_id', conversationId)
        .order('created_at', { ascending: true });

      if (error) throw error;

      // Fetch sender profiles separately to avoid join issues
      const senderIds = [...new Set((data || []).map(msg => msg.sender_id).filter(Boolean))];
      const { data: profiles } = await supabase
        .from('profiles')
        .select('id, display_name, avatar_url, username')
        .in('id', senderIds);

      const profileMap = new Map(profiles?.map(p => [p.id, p]) || []);

      return (data || []).map(msg => ({
        ...msg,
        sender: msg.sender_id ? profileMap.get(msg.sender_id) || {
          id: msg.sender_id,
          display_name: 'Unknown User',
          avatar_url: '',
          username: 'unknown'
        } : null
      })) as ChatMessage[];
    },
    enabled: !!conversationId
  });

  // Real-time subscription for new messages
  useEffect(() => {
    if (!conversationId) return;

    const channel = supabase
      .channel(`messages:${conversationId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: `conversation_id=eq.${conversationId}`
        },
        () => {
          queryClient.invalidateQueries({
            queryKey: ['messages', conversationId]
          });
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [conversationId, queryClient]);

  const sendMessage = useMutation({
    mutationFn: async (input: SendMessageInput | string) => {
      if (!user || !conversationId) {
        throw new Error("Missing required information to send message");
      }

      const content = typeof input === 'string' ? input : input.content;
      const metadata = typeof input === 'object' ? input.metadata : undefined;

      const { data, error } = await supabase
        .from('messages')
        .insert({
          sender_id: user.id,
          conversation_id: conversationId,
          content: content.trim(),
          message_type: 'text',
          metadata
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['messages', conversationId]
      });
    }
  });

  return {
    data: messages,
    isLoading,
    sendMessage: sendMessage.mutate
  };
}
