
import React, { useState } from 'react';
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Activity } from "@/hooks/use-activities";
import { Button } from "@/components/ui/button";
import { X, Users, Info, MessageSquare, MapPin } from "lucide-react";
import { ActivityGroupChat } from './ActivityGroupChat';
import { ActivityJoin } from './ActivityJoin';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

interface EnhancedActivityDetailsModalProps {
  activity: Activity;
  isOpen: boolean;
  onClose: () => void;
  initialTab?: string;
}

export function EnhancedActivityDetailsModal({
  activity,
  isOpen,
  onClose,
  initialTab = "details" // Default to details tab if not specified
}: EnhancedActivityDetailsModalProps) {
  const [activeTab, setActiveTab] = useState(initialTab);

  const handleClose = () => {
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[900px] p-0">
        <div className="flex items-center justify-between bg-gradient-to-r from-primary to-primary-purple p-4">
          <h2 className="text-xl font-bold text-white">{activity.title}</h2>
          <Button variant="ghost" size="icon" onClick={handleClose} className="text-white hover:bg-white/20">
            <X className="h-5 w-5" />
          </Button>
        </div>

        <Tabs 
          value={activeTab} 
          onValueChange={setActiveTab} 
          className="w-full"
        >
          <TabsList className="grid grid-cols-3 px-4 py-2">
            <TabsTrigger value="details" className="data-[state=active]:bg-primary data-[state=active]:text-white">
              <Info className="h-4 w-4 mr-2" />
              Details
            </TabsTrigger>
            <TabsTrigger value="participants" className="data-[state=active]:bg-primary data-[state=active]:text-white">
              <Users className="h-4 w-4 mr-2" />
              Participants
            </TabsTrigger>
            <TabsTrigger value="chat" className="data-[state=active]:bg-primary data-[state=active]:text-white">
              <MessageSquare className="h-4 w-4 mr-2" />
              Chat
            </TabsTrigger>
          </TabsList>

          <TabsContent value="details" className="p-4">
            <div className="space-y-4">
              {activity.media_urls && activity.media_urls[0] && (
                <div className="w-full h-48 overflow-hidden rounded-md">
                  <img 
                    src={activity.media_urls[0]} 
                    alt={activity.title} 
                    className="w-full h-full object-cover" 
                  />
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="text-lg font-medium">About this activity</h3>
                  <p className="mt-2 text-muted-foreground">
                    {activity.description || "No description provided."}
                  </p>

                  <div className="mt-4">
                    <h4 className="font-medium">When</h4>
                    <div className="flex items-center mt-1 text-sm text-muted-foreground">
                      <div>
                        {format(new Date(activity.start_time), "MMM d, yyyy h:mm a")}
                        {activity.end_time && ` - ${format(new Date(activity.end_time), "h:mm a")}`}
                      </div>
                    </div>
                  </div>

                  {activity.address && (
                    <div className="mt-4">
                      <h4 className="font-medium">Where</h4>
                      <div className="flex items-center mt-1 text-sm text-muted-foreground">
                        <MapPin className="h-4 w-4 mr-2 text-primary" />
                        {activity.address}
                      </div>
                    </div>
                  )}
                </div>

                <div className={cn(
                  "p-4 rounded-lg",
                  activity.is_paid ? "bg-amber-50" : "bg-green-50"
                )}>
                  <h3 className="text-lg font-medium mb-4">Join this activity</h3>

                  {activity.is_paid ? (
                    <div className="mb-4">
                      <div className="font-medium text-amber-700">Paid activity</div>
                      <div className="text-2xl font-bold text-amber-600">${activity.price?.toFixed(2)}</div>
                      <p className="text-sm text-muted-foreground mt-1">
                        Payment will be processed securely through our platform.
                      </p>
                    </div>
                  ) : (
                    <div className="mb-4">
                      <div className="font-medium text-green-700">Free activity</div>
                      <p className="text-sm text-muted-foreground mt-1">
                        Join this activity at no cost.
                      </p>
                    </div>
                  )}

                  <div className="space-y-4">
                    <ActivityJoin activity={activity} />
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="participants" className="p-4">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Activity Participants</h3>
              {/* Participants information would go here */}
              <p className="text-muted-foreground">
                This feature is coming soon. Check back later to see who's joining this activity.
              </p>
            </div>
          </TabsContent>

          <TabsContent value="chat" className="p-0">
            <ActivityGroupChat activity={activity} />
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
