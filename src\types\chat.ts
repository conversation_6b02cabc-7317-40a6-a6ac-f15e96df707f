
export interface ChatContainerProps {
  conversationId: string;
  activityId?: string;
}

export interface ChatMessage {
  id: string;
  conversation_id: string;
  sender_id: string;
  content: string;
  created_at: string;
  is_read: boolean;
  message_type?: 'text' | 'media' | 'location' | 'activity' | 'system';
  sender?: {
    id: string;
    display_name?: string;
    avatar_url?: string;
    username?: string;
  } | null;
  recipient_id?: string;
  media_url?: string;
  location_data?: any;
  is_admin_message?: boolean;
  is_admin?: boolean;
  is_system_message?: boolean;
}

export interface Message extends ChatMessage {
  // Adding Message type for compatibility with ChatMessages component
}

export interface Conversation {
  id: string;
  created_at: string;
  updated_at: string;
  last_message?: string;
  last_message_at?: string;
  is_group: boolean;
  title?: string;
  activity_id?: string;
  is_admin_conversation?: boolean;
  is_support?: boolean;
  participants?: {
    user_id: string;
    display_name?: string;
    avatar_url?: string;
    username?: string;
  }[];
}

export interface AdminConversation extends Conversation {
  is_admin_conversation: boolean;
  is_support?: boolean;
}

export interface SendMessageInput {
  content: string;
  conversationId?: string;
  attachment_urls?: string[];
  metadata?: Record<string, any>;
}

// BuddyProfile type for BuddyList component
export interface BuddyProfile {
  id: string;
  display_name?: string;
  username?: string;
  avatar_url?: string;
  bio?: string;
  is_verified?: boolean;
  last_seen_at?: string;
}

// Admin types for admin-related components
export interface AdminResponse {
  adminConversation: AdminConversation | null;
  supportConversation?: AdminConversation | null;
  isLoading: boolean;
  isError?: boolean;
  error?: Error;
  isAdmin?: boolean;
  sendAdminMessage?: {
    mutateAsync: (message: string) => Promise<any>;
    isPending: boolean;
  };
  isSupportLoading?: boolean;
  adminMessages?: ChatMessage[];
  refetch?: () => Promise<any>;
}
