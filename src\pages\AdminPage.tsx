import React, { useState } from 'react';
import { MainLayout } from "@/components/layouts/MainLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useAuth } from '@/hooks/use-auth';
import {
  Users, Search, Activity, CreditCard, Settings,
  BarChart, ChevronDown, Filter, Download, Loader2,
  MessageSquare, HelpCircle, Megaphone
} from 'lucide-react';
import {
  useIsAdmin,
  useAdminStats,
  useAdminUsers,
  useAdminActivities
} from '@/hooks/use-admin-data';
import { AdminMessagePanel } from '@/components/chat/AdminMessagePanel';
import { SupportChatPanel } from '@/components/admin/SupportChatPanel';
import { AdminAnnouncementForm } from '@/components/admin/AdminAnnouncementForm';

export default function AdminPage() {
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const { data: isAdmin, isLoading: isCheckingAdmin } = useIsAdmin();
  const { data: stats, isLoading: isLoadingStats } = useAdminStats();
  const { data: users, isLoading: isLoadingUsers } = useAdminUsers(searchQuery);
  const { data: activities, isLoading: isLoadingActivities } = useAdminActivities(searchQuery);

  if (isCheckingAdmin) {
    return (
      <MainLayout>
        <div className="flex flex-col items-center justify-center h-[calc(100vh-9rem)]">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="mt-4 text-muted-foreground">Checking permissions...</p>
        </div>
      </MainLayout>
    );
  }

  if (!isAdmin) {
    return (
      <MainLayout>
        <div className="flex flex-col items-center justify-center h-[calc(100vh-9rem)]">
          <h1 className="text-2xl font-bold text-red-500">Access Denied</h1>
          <p className="mt-2 text-gray-600">
            You don't have permission to access this page.
          </p>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container py-6 space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Admin Dashboard</h1>
          <div className="flex gap-2">
            <AdminMessagePanel />
            <Button variant="outline" size="sm">
              <Download className="mr-2 h-4 w-4" />
              Export Data
            </Button>
            <Button variant="outline" size="sm">
              <Settings className="mr-2 h-4 w-4" />
              Settings
            </Button>
          </div>
        </div>

        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">Total Users</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoadingStats ? (
                <div className="flex items-center space-x-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="text-sm text-muted-foreground">Loading...</span>
                </div>
              ) : (
                <>
                  <div className="text-2xl font-bold">{stats?.totalUsers.toLocaleString() || '0'}</div>
                  <p className="text-xs text-muted-foreground">
                    +{stats?.userGrowth || 0}% from last month
                  </p>
                </>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">Active Activities</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoadingStats ? (
                <div className="flex items-center space-x-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="text-sm text-muted-foreground">Loading...</span>
                </div>
              ) : (
                <>
                  <div className="text-2xl font-bold">{stats?.activeActivities.toLocaleString() || '0'}</div>
                  <p className="text-xs text-muted-foreground">
                    +{stats?.activityGrowth || 0}% from last month
                  </p>
                </>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">Active Users</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoadingStats ? (
                <div className="flex items-center space-x-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="text-sm text-muted-foreground">Loading...</span>
                </div>
              ) : (
                <>
                  <div className="text-2xl font-bold">{stats?.activeUsers.toLocaleString() || '0'}</div>
                  <p className="text-xs text-muted-foreground">
                    {((stats?.activeUsers || 0) / (stats?.totalUsers || 1) * 100).toFixed(1)}% of total users
                  </p>
                </>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">Total Revenue</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoadingStats ? (
                <div className="flex items-center space-x-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="text-sm text-muted-foreground">Loading...</span>
                </div>
              ) : (
                <>
                  <div className="text-2xl font-bold">${(stats?.revenue || 0).toLocaleString()}</div>
                  <p className="text-xs text-muted-foreground">
                    From {stats?.totalTransactions || 0} transactions
                  </p>
                </>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">Total Revenue</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">$12,543.23</div>
              <p className="text-xs text-muted-foreground">+18% from last month</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">Content Reports</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">15</div>
              <p className="text-xs text-muted-foreground">-2% from last month</p>
            </CardContent>
          </Card>
        </div>

        <Card className="overflow-hidden">
          <Tabs defaultValue="users">
            <div className="border-b">
              <div className="flex items-center px-4 py-2">
                <TabsList>
                  <TabsTrigger value="users" className="flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    Users
                  </TabsTrigger>
                  <TabsTrigger value="activities" className="flex items-center gap-2">
                    <Activity className="h-4 w-4" />
                    Activities
                  </TabsTrigger>
                  <TabsTrigger value="announcements" className="flex items-center gap-2">
                    <Megaphone className="h-4 w-4" />
                    Announcements
                  </TabsTrigger>
                  <TabsTrigger value="support" className="flex items-center gap-2">
                    <HelpCircle className="h-4 w-4" />
                    Support
                  </TabsTrigger>
                  <TabsTrigger value="transactions" className="flex items-center gap-2">
                    <CreditCard className="h-4 w-4" />
                    Transactions
                  </TabsTrigger>
                  <TabsTrigger value="analytics" className="flex items-center gap-2">
                    <BarChart className="h-4 w-4" />
                    Analytics
                  </TabsTrigger>
                </TabsList>
                <div className="ml-auto flex items-center gap-2">
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search..."
                      className="w-[200px] pl-8"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                  <Button variant="outline" size="sm">
                    <Filter className="mr-2 h-4 w-4" />
                    Filters
                    <ChevronDown className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
            <TabsContent value="users" className="p-0">
              <div className="p-4">
                {isLoadingUsers ? (
                  <div className="flex justify-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : (
                  <>
                    {users && users.length > 0 ? (
                      <table className="w-full">
                        <thead>
                          <tr className="border-b">
                            <th className="text-left p-2">User ID</th>
                            <th className="text-left p-2">Name</th>
                            <th className="text-left p-2">Email</th>
                            <th className="text-left p-2">Status</th>
                            <th className="text-left p-2">Created</th>
                            <th className="text-left p-2">Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          {users.map((user) => (
                            <tr key={user.id} className="border-b">
                              <td className="p-2">{user.id.substring(0, 8)}...</td>
                              <td className="p-2 flex items-center">
                                <div className="w-8 h-8 rounded-full bg-gray-200 mr-2 overflow-hidden">
                                  {user.avatar_url ? (
                                    <img
                                      src={user.avatar_url}
                                      alt={user.display_name}
                                      className="w-full h-full object-cover"
                                    />
                                  ) : (
                                    <div className="w-full h-full flex items-center justify-center bg-primary text-primary-foreground text-xs font-bold">
                                      {user.display_name.substring(0, 2).toUpperCase()}
                                    </div>
                                  )}
                                </div>
                                {user.display_name}
                                {user.is_admin && (
                                  <span className="ml-2 px-1.5 py-0.5 rounded text-xs bg-blue-100 text-blue-800">
                                    Admin
                                  </span>
                                )}
                              </td>
                              <td className="p-2">{user.email}</td>
                              <td className="p-2">
                                {user.is_banned ? (
                                  <span className="px-2 py-1 rounded text-xs bg-red-100 text-red-800">Banned</span>
                                ) : (
                                  <span className="px-2 py-1 rounded text-xs bg-green-100 text-green-800">Active</span>
                                )}
                              </td>
                              <td className="p-2 text-sm text-gray-500">
                                {new Date(user.created_at).toLocaleDateString()}
                              </td>
                              <td className="p-2">
                                <Button variant="ghost" size="sm">Edit</Button>
                                {user.is_banned ? (
                                  <Button variant="ghost" size="sm" className="text-green-500">Unban</Button>
                                ) : (
                                  <Button variant="ghost" size="sm" className="text-red-500">Ban</Button>
                                )}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    ) : (
                      <div className="text-center py-8">
                        <p className="text-muted-foreground">No users found</p>
                      </div>
                    )}
                  </>
                )}
              </div>
            </TabsContent>
            <TabsContent value="activities" className="p-0">
              <div className="p-4">
                {isLoadingActivities ? (
                  <div className="flex justify-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : (
                  <>
                    {activities && activities.length > 0 ? (
                      <table className="w-full">
                        <thead>
                          <tr className="border-b">
                            <th className="text-left p-2">Title</th>
                            <th className="text-left p-2">Host</th>
                            <th className="text-left p-2">Date</th>
                            <th className="text-left p-2">Price</th>
                            <th className="text-left p-2">Status</th>
                            <th className="text-left p-2">Flags</th>
                            <th className="text-left p-2">Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          {activities.map((activity) => (
                            <tr key={activity.id} className="border-b">
                              <td className="p-2 font-medium">{activity.title}</td>
                              <td className="p-2">{activity.host_name}</td>
                              <td className="p-2">
                                {activity.start_time ? new Date(activity.start_time).toLocaleDateString() : 'Not set'}
                              </td>
                              <td className="p-2">
                                {activity.is_paid ? `$${activity.price}` : 'Free'}
                              </td>
                              <td className="p-2">
                                {activity.status === 'cancelled' ? (
                                  <span className="px-2 py-1 rounded text-xs bg-red-100 text-red-800">Cancelled</span>
                                ) : activity.status === 'completed' ? (
                                  <span className="px-2 py-1 rounded text-xs bg-gray-100 text-gray-800">Completed</span>
                                ) : (
                                  <span className="px-2 py-1 rounded text-xs bg-green-100 text-green-800">Active</span>
                                )}
                              </td>
                              <td className="p-2">
                                {activity.flag_count > 0 ? (
                                  <span className="px-2 py-1 rounded text-xs bg-amber-100 text-amber-800">
                                    {activity.flag_count} flags
                                  </span>
                                ) : (
                                  <span className="text-gray-400">None</span>
                                )}
                              </td>
                              <td className="p-2">
                                <Button variant="ghost" size="sm">View</Button>
                                {activity.moderation_status !== 'approved' && (
                                  <Button variant="ghost" size="sm" className="text-green-500">Approve</Button>
                                )}
                                {activity.status !== 'cancelled' && (
                                  <Button variant="ghost" size="sm" className="text-red-500">Cancel</Button>
                                )}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    ) : (
                      <div className="text-center py-8">
                        <p className="text-muted-foreground">No activities found</p>
                      </div>
                    )}
                  </>
                )}
              </div>
            </TabsContent>
            <TabsContent value="announcements" className="p-0">
              <div className="p-4">
                <AdminAnnouncementForm />
              </div>
            </TabsContent>
            <TabsContent value="support" className="p-0">
              <div className="p-4">
                <SupportChatPanel />
              </div>
            </TabsContent>
            <TabsContent value="transactions" className="p-0">
              <div className="p-4">
                <p>Transactions management content will go here.</p>
              </div>
            </TabsContent>
            <TabsContent value="analytics" className="p-0">
              <div className="p-4">
                <p>Analytics dashboard content will go here.</p>
              </div>
            </TabsContent>
          </Tabs>
        </Card>
      </div>
    </MainLayout>
  );
}
