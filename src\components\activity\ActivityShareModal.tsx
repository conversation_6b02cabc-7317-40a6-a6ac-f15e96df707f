
import React from 'react';
import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Activity } from "@/hooks/use-activities";
import { ActivityShareModule } from './ActivityShareModule';

interface ActivityShareModalProps {
  activity: Activity;
  isOpen: boolean;
  onClose: () => void;
}

export function ActivityShareModal({ activity, isOpen, onClose }: ActivityShareModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Share Activity</DialogTitle>
          <DialogDescription>
            Share this activity with friends and other users
          </DialogDescription>
        </DialogHeader>
        
        <ActivityShareModule activity={activity} />
      </DialogContent>
    </Dialog>
  );
}
