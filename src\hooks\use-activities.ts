
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/use-auth';

export interface Activity {
  id: string;
  title: string;
  description?: string;
  location: any;
  address?: string;
  start_time: string;
  end_time?: string;
  max_participants?: number;
  price?: number;
  is_paid?: boolean;
  host_id: string;
  status?: string;
  visibility?: string;
  category_id?: string;
  media_urls?: string[];
  queue_type?: string;
  allow_waitlist?: boolean;
  group_chat_id?: string;
  moderation_status?: string;
  is_verified?: boolean;
  created_at: string;
  updated_at: string;
  host?: {
    id: string;
    display_name?: string;
    avatar_url?: string;
    username?: string;
  } | null;
  category?: {
    id: string;
    name: string;
    icon?: string;
    type?: string;
  } | null;
  participants?: any[];
  current_participants?: number;
}

export function useActivities() {
  const { user } = useAuth();

  const { data: activities, isLoading, error, refetch } = useQuery({
    queryKey: ['activities'],
    queryFn: async (): Promise<Activity[]> => {
      const { data, error } = await supabase
        .from('activities')
        .select(`
          *,
          host:profiles!activities_host_id_fkey (
            id,
            display_name,
            avatar_url,
            username
          ),
          category:categories!activities_category_id_fkey (
            id,
            name,
            icon,
            type
          )
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;

      return (data || []).map(activity => ({
        ...activity,
        allow_waitlist: activity.allow_waitlist ?? true,
        host: activity.host ? {
          id: activity.host.id || '',
          display_name: activity.host.display_name || '',
          avatar_url: activity.host.avatar_url || '',
          username: activity.host.username || ''
        } : null,
        category: activity.category ? {
          id: activity.category.id || '',
          name: activity.category.name || '',
          icon: activity.category.icon || '',
          type: activity.category.type || ''
        } : null
      })) as Activity[];
    }
  });

  const { data: userActivities, isLoading: isLoadingUserActivities } = useQuery({
    queryKey: ['user-activities', user?.id],
    queryFn: async (): Promise<Activity[]> => {
      if (!user) return [];

      const { data, error } = await supabase
        .from('activities')
        .select(`
          *,
          host:profiles!activities_host_id_fkey (
            id,
            display_name,
            avatar_url,
            username
          ),
          category:categories!activities_category_id_fkey (
            id,
            name,
            icon,
            type
          )
        `)
        .eq('host_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;

      return (data || []).map(data => ({
        ...data,
        allow_waitlist: data.allow_waitlist ?? true,
        host: data.host ? {
          id: data.host.id || '',
          display_name: data.host.display_name || '',
          avatar_url: data.host.avatar_url || '',
          username: data.host.username || ''
        } : null,
        category: data.category ? {
          id: data.category.id || '',
          name: data.category.name || '',
          icon: data.category.icon || '',
          type: data.category.type || ''
        } : null
      })) as Activity[];
    },
    enabled: !!user
  });

  return {
    activities: activities || [],
    userActivities: userActivities || [],
    isLoading,
    isLoadingUserActivities,
    error,
    refetch
  };
}

// Export a single activity hook for compatibility
export function useActivity(activityId: string) {
  return useQuery({
    queryKey: ['activity', activityId],
    queryFn: async (): Promise<Activity | null> => {
      if (!activityId) return null;

      const { data, error } = await supabase
        .from('activities')
        .select(`
          *,
          host:profiles!activities_host_id_fkey (
            id,
            display_name,
            avatar_url,
            username
          ),
          category:categories!activities_category_id_fkey (
            id,
            name,
            icon,
            type
          )
        `)
        .eq('id', activityId)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      if (!data) return null;

      return {
        ...data,
        allow_waitlist: data.allow_waitlist ?? true,
        host: data.host ? {
          id: data.host.id || '',
          display_name: data.host.display_name || '',
          avatar_url: data.host.avatar_url || '',
          username: data.host.username || ''
        } : null,
        category: data.category ? {
          id: data.category.id || '',
          name: data.category.name || '',
          icon: data.category.icon || '',
          type: data.category.type || ''
        } : null
      } as Activity;
    },
    enabled: !!activityId
  });
}
