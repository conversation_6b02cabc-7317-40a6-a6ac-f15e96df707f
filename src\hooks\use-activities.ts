
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/use-auth';

export interface Activity {
  id: string;
  title: string;
  description: string;
  location: any;
  address?: string;
  start_time: string;
  end_time: string; // Make this required to match types/activity.ts
  max_participants?: number;
  price?: number;
  is_paid: boolean;
  host_id: string;
  status: 'active' | 'cancelled' | 'completed';
  visibility: 'public' | 'private' | 'unlisted';
  category_id?: string;
  media_urls: string[];
  queue_type: 'fcfs' | 'priority' | 'fifo';
  allow_waitlist: boolean;
  group_chat_id?: string;
  moderation_status?: string;
  is_verified?: boolean;
  created_at: string;
  updated_at: string;
  host?: {
    id: string;
    display_name?: string;
    avatar_url?: string;
    username?: string;
  } | null;
  category?: {
    id: string;
    name: string;
    icon?: string;
    type?: string;
  } | null;
  participants?: any[];
  current_participants?: number;
}

export function useActivities() {
  const { user } = useAuth();

  const { data: activities, isLoading, error, refetch } = useQuery({
    queryKey: ['activities'],
    queryFn: async (): Promise<Activity[]> => {
      const { data, error } = await supabase
        .from('activities')
        .select(`
          *,
          category:categories!activities_category_id_fkey (
            id,
            name,
            icon,
            type
          )
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;

      return (data || []).map(activity => ({
        ...activity,
        description: activity.description || '', // Ensure description is always a string
        end_time: activity.end_time || activity.start_time, // Ensure end_time is always provided
        is_paid: activity.is_paid ?? false,
        media_urls: activity.media_urls || [],
        status: activity.status || 'active',
        visibility: activity.visibility || 'public',
        queue_type: activity.queue_type || 'fifo',
        allow_waitlist: activity.allow_waitlist ?? true,
        host: null, // We'll fetch host info separately if needed
        category: activity.category ? {
          id: activity.category.id || '',
          name: activity.category.name || '',
          icon: activity.category.icon || '',
          type: activity.category.type || ''
        } : null
      })) as Activity[];
    }
  });

  const { data: userActivities, isLoading: isLoadingUserActivities } = useQuery({
    queryKey: ['user-activities', user?.id],
    queryFn: async (): Promise<Activity[]> => {
      if (!user) return [];

      const { data, error } = await supabase
        .from('activities')
        .select(`
          *,
          category:categories!activities_category_id_fkey (
            id,
            name,
            icon,
            type
          )
        `)
        .eq('host_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;

      return (data || []).map(data => ({
        ...data,
        description: data.description || '', // Ensure description is always a string
        end_time: data.end_time || data.start_time, // Ensure end_time is always provided
        is_paid: data.is_paid ?? false,
        media_urls: data.media_urls || [],
        status: data.status || 'active',
        visibility: data.visibility || 'public',
        queue_type: data.queue_type || 'fifo',
        allow_waitlist: data.allow_waitlist ?? true,
        host: null, // We'll fetch host info separately if needed
        category: data.category ? {
          id: data.category.id || '',
          name: data.category.name || '',
          icon: data.category.icon || '',
          type: data.category.type || ''
        } : null
      })) as Activity[];
    },
    enabled: !!user
  });

  return {
    activities: activities || [],
    userActivities: userActivities || [],
    isLoading,
    isLoadingUserActivities,
    error,
    refetch
  };
}

// Export a single activity hook for compatibility
export function useActivity(activityId: string) {
  return useQuery({
    queryKey: ['activity', activityId],
    queryFn: async (): Promise<Activity | null> => {
      if (!activityId) return null;

      const { data, error } = await supabase
        .from('activities')
        .select(`
          *,
          category:categories!activities_category_id_fkey (
            id,
            name,
            icon,
            type
          )
        `)
        .eq('id', activityId)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      if (!data) return null;

      return {
        ...data,
        description: data.description || '', // Ensure description is always a string
        end_time: data.end_time || data.start_time, // Ensure end_time is always provided
        is_paid: data.is_paid ?? false,
        media_urls: data.media_urls || [],
        status: data.status || 'active',
        visibility: data.visibility || 'public',
        queue_type: data.queue_type || 'fifo',
        allow_waitlist: data.allow_waitlist ?? true,
        host: null, // We'll fetch host info separately if needed
        category: data.category ? {
          id: data.category.id || '',
          name: data.category.name || '',
          icon: data.category.icon || '',
          type: data.category.type || ''
        } : null
      } as Activity;
    },
    enabled: !!activityId
  });
}
