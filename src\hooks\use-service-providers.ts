
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/use-auth';

export interface ServiceProvider {
  id: string;
  user_id: string;
  name: string;
  bio: string;
  avatar_url: string | null;
  rating: number;
  total_reviews: number;
  hourly_rate: number;
  location: string;
  tags: string[];
  is_verified: boolean;
  created_at: string;
  updated_at: string;
  verification_level_id: string | null;
  trust_score: number;
  verification_status: string | null;
  services?: Service[];
}

export interface Service {
  id: string;
  provider_id: string;
  title: string;
  description: string;
  price: number;
  duration: number;
  category_id: string | null;
  is_featured: boolean;
  created_at: string;
  updated_at: string;
  category_name?: string;
  category_icon?: string;
}

// Hook to fetch all service providers
export function useServiceProviders(categoryId?: string, searchQuery?: string) {
  return useQuery({
    queryKey: ['service-providers', categoryId, searchQuery],
    queryFn: async (): Promise<ServiceProvider[]> => {
      try {
        // Mock implementation since service_providers table doesn't exist
        const mockServiceProviders: ServiceProvider[] = [
          {
            id: '1',
            user_id: 'user1',
            name: 'John Doe Services',
            bio: 'Professional services for all your needs',
            avatar_url: 'https://example.com/avatar1.jpg',
            rating: 4.8,
            total_reviews: 24,
            hourly_rate: 50,
            location: 'New York, NY',
            tags: ['web design', 'development', 'consulting'],
            is_verified: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            verification_level_id: '1',
            trust_score: 95,
            verification_status: 'verified',
            services: [
              {
                id: 'service1',
                provider_id: '1',
                title: 'Web Design',
                description: 'Professional web design services',
                price: 299,
                duration: 7,
                category_id: 'cat1',
                is_featured: true,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                category_name: 'Design',
                category_icon: 'design-icon'
              }
            ]
          },
          {
            id: '2',
            user_id: 'user2',
            name: 'Jane Smith Photography',
            bio: 'Professional photography services',
            avatar_url: 'https://example.com/avatar2.jpg',
            rating: 4.9,
            total_reviews: 36,
            hourly_rate: 75,
            location: 'Los Angeles, CA',
            tags: ['photography', 'events', 'portraits'],
            is_verified: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            verification_level_id: '2',
            trust_score: 98,
            verification_status: 'verified',
            services: [
              {
                id: 'service2',
                provider_id: '2',
                title: 'Event Photography',
                description: 'Professional event photography',
                price: 499,
                duration: 4,
                category_id: 'cat2',
                is_featured: true,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                category_name: 'Photography',
                category_icon: 'camera-icon'
              }
            ]
          }
        ];
        
        // Apply category filter if specified
        let filteredProviders = mockServiceProviders;
        if (categoryId) {
          filteredProviders = filteredProviders.filter(
            provider => provider.services?.some(service => service.category_id === categoryId)
          );
        }
        
        // Apply search filter if specified
        if (searchQuery) {
          const query = searchQuery.toLowerCase();
          filteredProviders = filteredProviders.filter(
            provider => 
              provider.name.toLowerCase().includes(query) ||
              provider.bio.toLowerCase().includes(query) ||
              provider.tags.some(tag => tag.toLowerCase().includes(query))
          );
        }
        
        return filteredProviders;
      } catch (error) {
        console.error('Error fetching service providers:', error);
        return [];
      }
    }
  });
}

// Hook to fetch a single service provider
export function useServiceProvider(providerId?: string) {
  return useQuery({
    queryKey: ['service-provider', providerId],
    queryFn: async (): Promise<ServiceProvider | null> => {
      if (!providerId) return null;
      
      try {
        // Mock implementation
        const mockServiceProviders: ServiceProvider[] = [
          {
            id: '1',
            user_id: 'user1',
            name: 'John Doe Services',
            bio: 'Professional services for all your needs',
            avatar_url: 'https://example.com/avatar1.jpg',
            rating: 4.8,
            total_reviews: 24,
            hourly_rate: 50,
            location: 'New York, NY',
            tags: ['web design', 'development', 'consulting'],
            is_verified: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            verification_level_id: '1',
            trust_score: 95,
            verification_status: 'verified',
            services: [
              {
                id: 'service1',
                provider_id: '1',
                title: 'Web Design',
                description: 'Professional web design services',
                price: 299,
                duration: 7,
                category_id: 'cat1',
                is_featured: true,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                category_name: 'Design',
                category_icon: 'design-icon'
              }
            ]
          },
          {
            id: '2',
            user_id: 'user2',
            name: 'Jane Smith Photography',
            bio: 'Professional photography services',
            avatar_url: 'https://example.com/avatar2.jpg',
            rating: 4.9,
            total_reviews: 36,
            hourly_rate: 75,
            location: 'Los Angeles, CA',
            tags: ['photography', 'events', 'portraits'],
            is_verified: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            verification_level_id: '2',
            trust_score: 98,
            verification_status: 'verified',
            services: [
              {
                id: 'service2',
                provider_id: '2',
                title: 'Event Photography',
                description: 'Professional event photography',
                price: 499,
                duration: 4,
                category_id: 'cat2',
                is_featured: true,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                category_name: 'Photography',
                category_icon: 'camera-icon'
              }
            ]
          }
        ];
        
        const provider = mockServiceProviders.find(p => p.id === providerId) || null;
        return provider;
      } catch (error) {
        console.error('Error fetching service provider:', error);
        return null;
      }
    },
    enabled: !!providerId
  });
}

// Hook to fetch services by provider
export function useProviderServices(providerId?: string) {
  return useQuery({
    queryKey: ['provider-services', providerId],
    queryFn: async (): Promise<Service[]> => {
      if (!providerId) return [];
      
      try {
        // Mock implementation
        const mockServices: Service[] = [
          {
            id: 'service1',
            provider_id: '1',
            title: 'Web Design',
            description: 'Professional web design services',
            price: 299,
            duration: 7,
            category_id: 'cat1',
            is_featured: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            category_name: 'Design',
            category_icon: 'design-icon'
          },
          {
            id: 'service2',
            provider_id: '1',
            title: 'Logo Design',
            description: 'Professional logo design',
            price: 149,
            duration: 3,
            category_id: 'cat1',
            is_featured: false,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            category_name: 'Design',
            category_icon: 'design-icon'
          }
        ];
        
        return mockServices.filter(service => service.provider_id === providerId);
      } catch (error) {
        console.error('Error fetching provider services:', error);
        return [];
      }
    },
    enabled: !!providerId
  });
}

// Hook to become a service provider
export function useBecomeProvider() {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const { toast } = useToast();
  
  return useMutation({
    mutationFn: async (data: {
      name: string;
      bio: string;
      hourly_rate: number;
      location: string;
      tags: string[];
    }): Promise<ServiceProvider> => {
      if (!user) throw new Error('You must be logged in to become a provider');
      
      // Mock implementation
      console.log('Creating provider with data:', data);
      
      // Create a mock provider
      const newProvider: ServiceProvider = {
        id: crypto.randomUUID(),
        user_id: user.id,
        name: data.name,
        bio: data.bio,
        avatar_url: null, // Will be filled from profile later
        rating: 0,
        total_reviews: 0,
        hourly_rate: data.hourly_rate,
        location: data.location,
        tags: data.tags,
        is_verified: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        verification_level_id: null,
        trust_score: 0,
        verification_status: 'pending',
        services: []
      };
      
      return newProvider;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['service-providers'] });
      toast({
        title: 'Success!',
        description: 'You are now registered as a service provider',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive'
      });
    }
  });
}
