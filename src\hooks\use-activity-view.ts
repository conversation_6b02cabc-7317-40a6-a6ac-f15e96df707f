
import { create } from 'zustand';
import { Activity } from '@/hooks/use-activities';

interface ActivityViewState {
  selectedActivity: Activity | null;
  setSelectedActivity: (activity: Activity | null) => void;
  viewOnMap: (activity: Activity) => void;
}

export const useActivityView = create<ActivityViewState>((set) => ({
  selectedActivity: null,
  setSelectedActivity: (activity) => set({ selectedActivity: activity }),
  viewOnMap: (activity) => {
    // This function will be used to center the map on the activity
    // and highlight the activity marker
    set({ selectedActivity: activity });
    
    // In a real implementation, we would navigate to the map page 
    // and center on the activity location
    // window.location.href = `/meetmap?activity=${activity.id}`;
  }
}));
