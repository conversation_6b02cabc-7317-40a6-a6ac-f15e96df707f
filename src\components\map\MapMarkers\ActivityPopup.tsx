
import React from 'react';
import { Activity } from '@/hooks/use-activities';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar, MapPin, Clock, Users, X, MessageSquare, DollarSign, Share2 } from 'lucide-react';
import { formatDistanceToNow, format } from 'date-fns';
import { cn } from '@/lib/utils';

interface ActivityPopupProps {
  activity: Activity;
  onClose: () => void;
  onJoin?: () => void;
  onChat?: () => void;
  onShare?: () => void;
  className?: string;
}

export const ActivityPopup = ({ 
  activity, 
  onClose, 
  onJoin, 
  onChat,
  onShare,
  className 
}: ActivityPopupProps) => {
  const formattedDate = activity.start_time ? 
    format(new Date(activity.start_time), 'EEE, MMM d • h:mm a') : 
    'Date not specified';

  const isUpcoming = activity.start_time && new Date(activity.start_time) > new Date();
  const timeUntil = activity.start_time ? 
    formatDistanceToNow(new Date(activity.start_time), { addSuffix: true }) : 
    'Unknown time';

  // Calculate spots left if max_participants is set
  const spotsLeft = activity.max_participants ? 
    `${Math.max(0, activity.max_participants)} spots available` : 
    'Unlimited spots';

  // Determine category emoji based on category name
  const getCategoryEmoji = (): string => {
    if (!activity.category?.name) return '📌';
    
    const categoryName = activity.category.name.toLowerCase();
    const emojiMap: Record<string, string> = {
      'sports': '🏀',
      'dining': '🍽️',
      'music': '🎵',
      'outdoors': '🌳',
      'art': '🎨',
      'technology': '💻',
      'gaming': '🎮',
      'education': '📚',
      'fitness': '💪',
      'social': '🎉',
      'dating': '❤️',
      'volunteering': '🤝',
    };
    
    return emojiMap[categoryName] || activity.category.icon || '📌';
  };

  return (
    <div className={cn(
      "bg-white/95 backdrop-blur-sm rounded-xl shadow-lg border border-border w-[320px] overflow-hidden",
      className
    )}>
      {/* Header with category emoji and gradient background */}
      <div className="relative bg-gradient-to-r from-blue-50 to-purple-50 p-4 pb-3 border-b border-border">
        <Button 
          variant="ghost" 
          size="icon" 
          className="absolute right-2 top-2 h-8 w-8 bg-white/80 hover:bg-white shadow-sm" 
          onClick={onClose}
        >
          <X className="h-4 w-4" />
        </Button>
        
        {/* Title and category */}
        <div className="pr-8">
          <div className="flex items-center gap-2 mb-1.5">
            <span className="text-2xl">
              {getCategoryEmoji()}
            </span>
            <Badge variant="outline" className="font-normal bg-white/80">
              {activity.category?.name || 'Activity'}
            </Badge>
            {activity.is_paid && (
              <Badge variant="secondary" className="bg-amber-100 text-amber-700 border-amber-200 font-normal">
                <DollarSign className="h-3 w-3 mr-0.5" />
                Paid
              </Badge>
            )}
          </div>
          <h3 className="text-lg font-medium">{activity.title}</h3>
        </div>
      </div>
      
      {/* Activity details */}
      <div className="px-4 py-3 space-y-2.5">
        {/* Date and time */}
        <div className="flex items-start">
          <Calendar className="h-4 w-4 mr-2 mt-0.5 text-blue-500 shrink-0" />
          <div>
            <div className="text-sm font-medium">{formattedDate}</div>
            <div className="text-xs text-muted-foreground">{timeUntil}</div>
          </div>
        </div>
        
        {/* Location */}
        {activity.address && (
          <div className="flex items-start">
            <MapPin className="h-4 w-4 mr-2 mt-0.5 text-rose-500 shrink-0" />
            <div className="text-sm">{activity.address}</div>
          </div>
        )}
        
        {/* Duration if end_time exists */}
        {activity.end_time && (
          <div className="flex items-start">
            <Clock className="h-4 w-4 mr-2 mt-0.5 text-purple-500 shrink-0" />
            <div className="text-sm">
              {format(new Date(activity.start_time), 'h:mm a')} - {format(new Date(activity.end_time), 'h:mm a')}
            </div>
          </div>
        )}
        
        {/* Participants limit */}
        {activity.max_participants && (
          <div className="flex items-start">
            <Users className="h-4 w-4 mr-2 mt-0.5 text-green-500 shrink-0" />
            <div className="text-sm">
              <span className="font-medium">{spotsLeft}</span>
            </div>
          </div>
        )}
        
        {/* Price */}
        <div className="flex items-start">
          <DollarSign className="h-4 w-4 mr-2 mt-0.5 text-amber-500 shrink-0" />
          <div className="text-sm">
            {activity.is_paid ? 
              <span className="font-medium">${activity.price?.toFixed(2) || '0.00'}</span> : 
              <span className="text-green-600 font-medium">Free</span>}
          </div>
        </div>
      </div>
      
      {/* Description */}
      {activity.description && (
        <div className="px-4 py-3 border-t border-border bg-slate-50/50">
          <p className="text-sm text-muted-foreground">{activity.description}</p>
        </div>
      )}
      
      {/* Action buttons */}
      <div className="p-4 pt-3 flex gap-2 bg-white border-t border-border">
        <Button 
          className="flex-1" 
          onClick={onJoin}
          disabled={!isUpcoming || !onJoin}
        >
          {activity.is_paid ? 'Pay & Join' : 'Join Activity'}
        </Button>
        <Button 
          variant="outline" 
          className="flex-1" 
          onClick={onChat}
          disabled={!onChat}
        >
          <MessageSquare className="h-4 w-4 mr-2" />
          Chat
        </Button>
        {onShare && (
          <Button 
            variant="ghost" 
            size="icon"
            onClick={onShare}
          >
            <Share2 className="h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  );
};
