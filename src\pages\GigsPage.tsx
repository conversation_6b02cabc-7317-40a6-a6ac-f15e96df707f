
import React, { useState } from 'react';
import { MainLayout } from "@/components/layouts/MainLayout";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { GigCard } from "@/components/gigs/GigCard";
import { GigCreationModal } from "@/components/gigs/GigCreationModal";
import { useGigs } from "@/hooks/use-gigs";
import { useServiceCategories, useUserServiceProvider } from "@/hooks/use-service-provider";
import { Search, Plus, Briefcase, Loader2 } from "lucide-react";

const GigsPage: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("");
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  const { data: gigs, isLoading, error } = useGigs();
  const { data: categories } = useServiceCategories();
  const { data: userProvider } = useUserServiceProvider();

  if (isLoading) {
    return (
      <MainLayout title="Gigs">
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </MainLayout>
    );
  }

  if (error) {
    return (
      <MainLayout title="Gigs">
        <div className="text-center py-8">
          <p className="text-red-600">Error loading gigs: {error.message}</p>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout title="Gigs">
      <div className="container mx-auto px-4 py-8">
        {/* Header Section */}
        <Card className="mb-8 border-none bg-gradient-to-r from-primary/10 to-secondary/10 shadow-sm">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <Briefcase className="h-5 w-5 text-primary" />
                  <CardTitle>Gigs Marketplace</CardTitle>
                </div>
                <CardDescription>
                  Find talented buddies offering amazing services. From creative work to professional services,
                  discover the perfect gig for your needs.
                </CardDescription>
              </div>
              {userProvider && (
                <Button onClick={() => setIsCreateModalOpen(true)} className="gap-2">
                  <Plus className="h-4 w-4" />
                  Create Gig
                </Button>
              )}
            </div>
          </CardHeader>
        </Card>

        {/* Search and Filters */}
        <Card className="mb-8 border shadow-sm">
          <CardHeader>
            <div className="flex flex-col md:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search gigs..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-9"
                />
              </div>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-full md:w-[200px]">
                  <SelectValue placeholder="All Categories" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Categories</SelectItem>
                  {categories?.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardHeader>
        </Card>

        {/* Gigs Grid */}
        {gigs && gigs.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {gigs.map((gig) => (
              <GigCard key={gig.id} gig={gig} />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <Briefcase className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No gigs found</h3>
            <p className="text-gray-600 mb-4">
              {searchQuery || selectedCategory
                ? "Try adjusting your search or filters"
                : "Be the first to create a gig!"}
            </p>
            {userProvider && (
              <Button onClick={() => setIsCreateModalOpen(true)}>
                Create Your First Gig
              </Button>
            )}
          </div>
        )}

        <GigCreationModal
          isOpen={isCreateModalOpen}
          onClose={() => setIsCreateModalOpen(false)}
        />
      </div>
    </MainLayout>
  );
};

export default GigsPage;
