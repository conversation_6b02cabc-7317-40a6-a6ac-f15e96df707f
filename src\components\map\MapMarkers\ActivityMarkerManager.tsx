
import React, { useEffect, useRef, useState } from 'react';
import mapboxgl from 'mapbox-gl';
import { createRoot } from 'react-dom/client';
import { Activity } from '@/hooks/use-activities';
import { ActivityMapMarker } from './ActivityMapMarker';

interface ActivityMarkerManagerProps {
  map: mapboxgl.Map;
  activities: Activity[];
  selectedActivity: Activity | null;
  onActivityClick: (activity: Activity) => void;
  showActivities: boolean;
}

export function ActivityMarkerManager({
  map,
  activities,
  selectedActivity,
  onActivityClick,
  showActivities
}: ActivityMarkerManagerProps) {
  const markersRef = useRef<{ [key: string]: mapboxgl.Marker }>({});
  const elementsRef = useRef<{ [key: string]: HTMLDivElement }>({});
  const rootsRef = useRef<{ [key: string]: any }>({});
  
  // Track recently created activities
  const [recentActivities, setRecentActivities] = useState<Record<string, boolean>>({});
  
  // Process activities when they change
  useEffect(() => {
    if (!map || !showActivities) {
      // Clean up markers if activities shouldn't be shown
      Object.entries(markersRef.current).forEach(([id, marker]) => {
        marker.remove();
      });
      markersRef.current = {};
      return;
    }
    
    // Find which activities are new (added in the last minute)
    const now = Date.now();
    const newActivities: Record<string, boolean> = {};
    activities.forEach(activity => {
      const createdTime = new Date(activity.created_at).getTime();
      if (now - createdTime < 60000) { // 1 minute
        newActivities[activity.id] = true;
      }
    });
    
    // Update recent activities if there are new ones
    if (Object.keys(newActivities).length > 0) {
      setRecentActivities(prev => ({...prev, ...newActivities}));
      
      // Clear new activities indicator after a while
      setTimeout(() => {
        setRecentActivities({});
      }, 5000);
    }
    
    // Remove markers for activities that no longer exist
    Object.keys(markersRef.current).forEach(id => {
      if (!activities.find(a => a.id === id)) {
        markersRef.current[id].remove();
        delete markersRef.current[id];
        delete elementsRef.current[id];
        if (rootsRef.current[id]) {
          rootsRef.current[id].unmount();
          delete rootsRef.current[id];
        }
      }
    });
    
    // Create or update markers for each activity
    activities.forEach(activity => {
      if (!activity.location) return;
      
      // Check if this marker already exists
      if (markersRef.current[activity.id]) {
        // Update marker position
        markersRef.current[activity.id].setLngLat([activity.location.x, activity.location.y]);
        
        // Re-render React component to update selection/new status
        if (rootsRef.current[activity.id]) {
          rootsRef.current[activity.id].render(
            <ActivityMapMarker
              activity={activity}
              isSelected={selectedActivity?.id === activity.id}
              isNew={!!recentActivities[activity.id]}
              onClick={() => onActivityClick(activity)}
            />
          );
        }
      } 
      // Create a new marker if it doesn't exist
      else {
        // Create container element
        const el = document.createElement('div');
        elementsRef.current[activity.id] = el;
        
        // Create React root
        const root = createRoot(el);
        rootsRef.current[activity.id] = root;
        
        // Render marker component
        root.render(
          <ActivityMapMarker
            activity={activity}
            isSelected={selectedActivity?.id === activity.id}
            isNew={!!recentActivities[activity.id]}
            onClick={() => onActivityClick(activity)}
          />
        );
        
        // Create and add Mapbox marker
        const marker = new mapboxgl.Marker({
          element: el,
          anchor: 'bottom'
        })
          .setLngLat([activity.location.x, activity.location.y])
          .addTo(map);
        
        markersRef.current[activity.id] = marker;
      }
    });
    
    // Cleanup function
    return () => {
      Object.entries(markersRef.current).forEach(([id, marker]) => {
        marker.remove();
      });
      
      Object.entries(rootsRef.current).forEach(([id, root]) => {
        if (root && root.unmount) {
          root.unmount();
        }
      });
      
      markersRef.current = {};
      elementsRef.current = {};
      rootsRef.current = {};
    };
  }, [map, activities, selectedActivity, onActivityClick, showActivities, recentActivities]);
  
  return null; // This component doesn't render anything directly
}
